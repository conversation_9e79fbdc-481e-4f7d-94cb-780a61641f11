# <PERSON><PERSON><PERSON><PERSON> kütüphaneleri yükle
!pip install -q unsloth[colab-new] > /dev/null
!pip install -q gradio > /dev/null
!pip install -q opencv-python mediapipe > /dev/null
!pip install -q plotly > /dev/null

# Audio kütüphaneleri (opsiyonel)
try:
    !pip install -q pyttsx3 speechrecognition sounddevice soundfile librosa > /dev/null
    print("✅ Audio libraries installed")
except:
    print("⚠️ Audio libraries installation failed - continuing without audio")

# Screen monitoring kütüphaneleri (opsiyonel)
try:
    !pip install -q pyautogui pygetwindow psutil > /dev/null
    print("✅ Screen monitoring libraries installed")
except:
    print("⚠️ Screen monitoring libraries installation failed - continuing without screen monitoring")

print("🎉 Installation completed!")

# Ana modülleri import et
from gemma_coach_core import GemmaCoachCore, initialize_coach, get_coach
from screen_monitor import OptionalScreenMonitor, initialize_screen_monitor, get_screen_monitor
from camera_monitor import CameraMonitor, initialize_camera_monitor, get_camera_monitor
from session_manager import SessionManager, initialize_session_manager, get_session_manager

# <PERSON><PERSON><PERSON> gerek<PERSON> imports
import gradio as gr
import torch
import time
import threading
from typing import Dict, Any, Optional, Tuple
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime

print("📦 All modules imported successfully!")
print(f"🔧 PyTorch CUDA available: {torch.cuda.is_available()}")
print(f"🔧 Device: {'CUDA' if torch.cuda.is_available() else 'CPU'}")

# Global instances oluştur
print("🚀 Initializing AI Coach System...")

# Ana AI Coach
coach = initialize_coach("unsloth/Gemma-2-9B-It-GGUF")
print("✅ Gemma Coach Core initialized")

# Screen Monitor (opsiyonel)
screen_monitor = initialize_screen_monitor()
print("✅ Screen Monitor initialized")

# Camera Monitor
camera_monitor = initialize_camera_monitor()
print("✅ Camera Monitor initialized")

# Session Manager
session_manager = initialize_session_manager()
print("✅ Session Manager initialized")

print("\n🎉 All systems ready!")

# Gemma 3N modelini yükle
print("📥 Loading Gemma 3N model...")
print("⏳ This may take a few minutes on first run...")

model_loaded = coach.load_model(
    max_seq_length=2048,
    load_in_4bit=True  # Memory efficient loading
)

if model_loaded:
    print("\n🎉 Gemma 3N model loaded successfully!")
    print("🤖 AI Coach is now powered by real Gemma 3N multimodal capabilities")
else:
    print("\n⚠️ Model loading failed - using simulation mode")
    print("🔄 AI Coach will work with enhanced simulations")

# Model bilgilerini göster
model_info = coach.get_model_info()
print("\n📊 System Status:")
for key, value in model_info.items():
    print(f"   {key}: {value}")

def start_study_session():
    """Çalışma seansı başlat"""
    # Session başlat
    result = session_manager.start_session()
    
    if not result['success']:
        return result['message'], "❌ Error", "0 minutes", "Error", "📺 Error"
    
    # Kamera izlemeyi başlat
    camera_started = camera_monitor.start_monitoring()
    
    # Ekran izleme durumunu kontrol et (sadece etkinse başlat)
    screen_status = screen_monitor.get_status()
    
    # AI Coach'tan başlangıç mesajı al
    context = {
        'duration': 0,
        'session_count': session_manager.session_count,
        'camera_active': camera_started
    }
    
    feedback = coach.generate_coaching_response('start', context)
    
    return (
        "🟢 Session Started", 
        feedback, 
        "0 minutes", 
        "Starting",
        screen_status
    )

def end_study_session():
    """Çalışma seansı sonlandır"""
    # Session sonlandır
    result = session_manager.end_session()
    
    if not result['success']:
        return result['message'], "❌ Error", "0 minutes", "Error", "📺 Error"
    
    # Kamera izlemeyi durdur
    camera_monitor.stop_monitoring()
    
    # AI Coach'tan bitiş mesajı al
    context = {
        'duration': result['duration_minutes'],
        'session_summary': result['session_summary']
    }
    
    feedback = coach.generate_coaching_response('end', context)
    
    duration_text = f"{result['duration_minutes']:.1f} minutes"
    screen_status = screen_monitor.get_status()
    
    return (
        "🔴 Session Completed", 
        feedback, 
        duration_text, 
        "Completed",
        screen_status
    )

def enable_screen_monitoring():
    """Ekran izlemeyi etkinleştir"""
    message = screen_monitor.enable_monitoring(True)
    if screen_monitor.start_monitoring():
        return f"✅ {message} - Aktif olarak izleniyor"
    else:
        return f"⚠️ {message} - Başlatılamadı (kütüphaneler eksik olabilir)"

def disable_screen_monitoring():
    """Ekran izlemeyi devre dışı bırak"""
    message = screen_monitor.enable_monitoring(False)
    return f"🚫 {message}"

def generate_audio_feedback(text: str, audio_enabled: bool) -> Optional[str]:
    """Sesli geri bildirim üret"""
    if not audio_enabled:
        return None
    return coach.generate_audio_feedback(text)

def refresh_status():
    """Durumu yenile"""
    status_update = session_manager.get_status_update()
    screen_status = screen_monitor.get_status()
    
    return (
        status_update['status'], 
        status_update['feedback'], 
        status_update['duration'], 
        status_update['attention'],
        screen_status
    )

def create_progress_chart():
    """İlerleme grafiği oluştur"""
    try:
        daily_progress = session_manager.get_daily_progress()
        
        # Günlük ilerleme grafiği
        fig = go.Figure()
        
        # Progress bar
        fig.add_trace(go.Bar(
            x=['Bugün'],
            y=[daily_progress['total_minutes']],
            name='Tamamlanan',
            marker_color='#2E8B57'
        ))
        
        # Goal line
        fig.add_hline(
            y=daily_progress['daily_goal_minutes'],
            line_dash="dash",
            line_color="red",
            annotation_text=f"Hedef: {daily_progress['daily_goal_minutes']} dk"
        )
        
        fig.update_layout(
            title=f"Günlük İlerleme - %{daily_progress['progress_percentage']:.1f}",
            yaxis_title="Dakika",
            height=300
        )
        
        return fig
        
    except Exception as e:
        print(f"Chart error: {e}")
        fig = go.Figure()
        fig.add_annotation(text=f"Error: {str(e)}", xref="paper", yref="paper", 
                         x=0.5, y=0.5, showarrow=False)
        return fig

print("🎯 Helper functions defined!")

# Gradio arayüzü oluştur
with gr.Blocks(title="🤖 Gemma 3N AI Coach", theme=gr.themes.Soft()) as app:
    gr.HTML("""
    <div style="text-align: center; padding: 20px;">
        <h1>🤖 Gemma 3N AI Study Coach</h1>
        <p>Gelişmiş AI koçluk sistemi - Multimodal, Sesli Geri Bildirim, İsteğe Bağlı Ekran İzleme</p>
    </div>
    """)
    
    with gr.Row():
        # Sol panel - Kontroller
        with gr.Column(scale=1):
            gr.HTML("<h3>🎮 Session Controls</h3>")
            
            with gr.Row():
                start_btn = gr.Button(
                    "🟢 Start Study Session", 
                    variant="primary", 
                    size="lg",
                    scale=2
                )
                end_btn = gr.Button(
                    "🔴 End Session", 
                    variant="stop", 
                    size="lg",
                    scale=2
                )
            
            refresh_btn = gr.Button(
                "🔄 Refresh Status", 
                variant="secondary",
                size="sm"
            )
            
            # Ekran izleme kontrolleri
            gr.HTML("<h4>📺 Screen Monitoring</h4>")
            with gr.Row():
                screen_enable_btn = gr.Button(
                    "📺 Etkinleştir",
                    variant="secondary",
                    size="sm"
                )
                screen_disable_btn = gr.Button(
                    "🚫 Kapat",
                    variant="secondary",
                    size="sm"
                )
            
            # Sesli geri bildirim kontrolü
            gr.HTML("<h4>🔊 Audio Feedback</h4>")
            audio_enable_checkbox = gr.Checkbox(
                label="Sesli Geri Bildirim",
                value=coach.get_model_info()['audio_available']
            )
            
            # Durum göstergeleri
            gr.HTML("<h3>📈 Status</h3>")
            
            session_status = gr.Textbox(
                label="🎯 Session Status",
                value="Ready to Start",
                interactive=False
            )
            
            duration_display = gr.Textbox(
                label="⏱️ Duration",
                value="0 minutes",
                interactive=False
            )
            
            attention_display = gr.Textbox(
                label="🧠 Attention Level",
                value="Ready",
                interactive=False
            )
            
            screen_status_display = gr.Textbox(
                label="📺 Screen Status",
                value="Devre dışı - isteğe bağlı etkinleştirilebilir",
                interactive=False
            )
        
        # Sağ panel - AI Coach ve Analytics
        with gr.Column(scale=2):
            gr.HTML("<h3>🤖 Your AI Study Coach</h3>")
            
            ai_feedback = gr.Textbox(
                label="💬 Personalized Coaching Messages",
                value="Merhaba! Ben senin AI çalışma koçunum. Gemma 3N ile güçlendirilmiş gelişmiş yeteneklerim var. 'Start Session' butonuna tıklayarak başlayalım!",
                lines=4,
                interactive=False
            )
            
            # Sesli çıkış
            audio_output = gr.Audio(
                label="🎵 AI Coach Voice Message",
                visible=coach.get_model_info()['audio_available']
            )
            
            # Analytics
            gr.HTML("<h3>📊 Progress Analytics</h3>")
            
            progress_chart = gr.Plot(
                label="Daily Progress Chart",
                value=create_progress_chart()
            )
            
            # Sistem bilgileri
            with gr.Accordion("🔧 System Information", open=False):
                model_info = coach.get_model_info()
                info_text = "\n".join([f"{k}: {v}" for k, v in model_info.items()])
                gr.Textbox(
                    label="System Status",
                    value=info_text,
                    lines=6,
                    interactive=False
                )
    
    # Event handlers
    start_btn.click(
        start_study_session,
        outputs=[
            session_status, ai_feedback, duration_display, 
            attention_display, screen_status_display
        ]
    )
    
    end_btn.click(
        end_study_session,
        outputs=[
            session_status, ai_feedback, duration_display, 
            attention_display, screen_status_display
        ]
    )
    
    refresh_btn.click(
        refresh_status,
        outputs=[
            session_status, ai_feedback, duration_display, 
            attention_display, screen_status_display
        ]
    )
    
    screen_enable_btn.click(
        enable_screen_monitoring,
        outputs=[screen_status_display]
    )
    
    screen_disable_btn.click(
        disable_screen_monitoring,
        outputs=[screen_status_display]
    )
    
    # Otomatik yenileme (her 30 saniyede)
    def auto_refresh():
        while True:
            time.sleep(30)
            if session_manager.active:
                # Sadece aktif seans varsa yenile
                pass
    
    # Auto-refresh thread başlat
    auto_refresh_thread = threading.Thread(target=auto_refresh, daemon=True)
    auto_refresh_thread.start()

print("🎨 Gradio interface created!")

# Uygulamayı başlat
print("🚀 Starting Gemma 3N AI Coach...")
print("📱 Interface will be available at the provided URL")
print("🎯 Ready for enhanced AI coaching experience!")

# Kaggle ortamında public link ile başlat
app.launch(
    share=True,  # Public link oluştur
    server_name="0.0.0.0",  # Tüm IP'lerden erişim
    server_port=7860,  # Port
    show_error=True,  # Hataları göster
    quiet=False  # Verbose output
)

# Test fonksiyonları
def run_quick_test():
    """Hızlı sistem testi"""
    print("🧪 Running quick system test...")
    
    # 1. Model testi
    test_context = {
        'duration': 5,
        'attention_score': 85,
        'face_detected': True
    }
    
    response = coach.generate_coaching_response('progress', test_context)
    print(f"✅ AI Response: {response}")
    
    # 2. Session manager testi
    session_info = session_manager.get_current_session_info()
    print(f"✅ Session Info: {session_info['active']}")
    
    # 3. Screen monitor testi
    screen_status = screen_monitor.get_status()
    print(f"✅ Screen Status: {screen_status}")
    
    # 4. Camera monitor testi
    camera_status = camera_monitor.get_current_status()
    print(f"✅ Camera Status: {camera_status['monitoring_active']}")
    
    print("🎉 Quick test completed!")

# Test çalıştır
run_quick_test()