#!/usr/bin/env python3
"""
Test script for Enhanced Gemma 3N AI Coach with Audio Feedback and Optional Screen Monitoring
"""

import sys
import time
import numpy as np
from typing import Dict, Any, Optional

# Test imports
try:
    import torch
    print("✅ PyTorch available")
except ImportError:
    print("❌ PyTorch not available")
    sys.exit(1)

try:
    from unsloth import FastLanguageModel
    print("✅ Unsloth available")
except ImportError:
    print("❌ Unsloth not available - install with: pip install unsloth")
    sys.exit(1)

# Audio processing test
try:
    import pyttsx3
    import speech_recognition as sr
    import sounddevice as sd
    import soundfile as sf
    import librosa
    AUDIO_AVAILABLE = True
    print("✅ Audio processing libraries ready")
except ImportError:
    AUDIO_AVAILABLE = False
    print("⚠️ Audio features disabled - install: pip install pyttsx3 speechrecognition sounddevice soundfile librosa")

# Screen monitoring test
try:
    import pyautogui
    import pygetwindow as gw
    SCREEN_MONITORING_AVAILABLE = True
    print("✅ Screen monitoring libraries ready")
except ImportError:
    SCREEN_MONITORING_AVAILABLE = False
    print("⚠️ Screen monitoring disabled - install: pip install pyautogui pygetwindow")

class TestEnhancedAICoach:
    """Test version of Enhanced AI Coach"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🔧 Using device: {self.device}")
        
    def load_model(self):
        """Load Gemma 3N model for testing"""
        try:
            print("📥 Loading Gemma 3N model...")
            
            # Load model with Unsloth
            self.model, self.tokenizer = FastLanguageModel.from_pretrained(
                model_name="unsloth/Gemma-2-9B-It-GGUF",
                max_seq_length=2048,
                dtype=None,
                load_in_4bit=True,
            )
            
            # Enable inference mode
            FastLanguageModel.for_inference(self.model)
            
            print("✅ Gemma 3N model loaded successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Model loading failed: {e}")
            return False
    
    def test_multimodal_response(self, situation: str, context: Dict[str, Any]) -> str:
        """Test multimodal response generation"""
        try:
            # Create multimodal messages for Gemma 3N
            messages = [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": "Sen gelişmiş bir AI eğitim koçusun. Kısa, motive edici tavsiyelerde bulun."}]
                },
                {
                    "role": "user", 
                    "content": [{"type": "text", "text": f"Durum: {situation}. Çalışma süresi: {context.get('duration', 0)} dakika. Dikkat seviyesi: %{context.get('attention_score', 75)}"}]
                }
            ]
            
            # Apply chat template
            inputs = self.tokenizer.apply_chat_template(
                messages,
                add_generation_prompt=True,
                tokenize=True,
                return_dict=True,
                return_tensors="pt"
            ).to(self.device)
            
            # Generate with Gemma 3N recommended settings
            with torch.no_grad():
                output_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=128,
                    temperature=1.0,
                    top_p=0.95,
                    top_k=64,
                    do_sample=True
                )
            
            # Decode response
            generated_text = self.tokenizer.decode(
                output_ids[0][inputs['input_ids'].shape[-1]:], 
                skip_special_tokens=True
            )
            
            return generated_text.strip()
            
        except Exception as e:
            print(f"❌ Response generation failed: {e}")
            return f"Test response for situation: {situation}"
    
    def test_audio_feedback(self, text: str) -> Optional[str]:
        """Test audio feedback generation"""
        if not AUDIO_AVAILABLE:
            print("⚠️ Audio not available for testing")
            return None
        
        try:
            print("🔊 Testing audio feedback...")
            engine = pyttsx3.init()
            
            # Configure voice
            voices = engine.getProperty('voices')
            if voices:
                engine.setProperty('voice', voices[0].id)
            
            engine.setProperty('rate', 150)
            engine.setProperty('volume', 0.8)
            
            # Test audio generation
            audio_file = f"test_audio_{int(time.time())}.wav"
            engine.save_to_file(text, audio_file)
            engine.runAndWait()
            
            print(f"✅ Audio generated: {audio_file}")
            return audio_file
            
        except Exception as e:
            print(f"❌ Audio generation failed: {e}")
            return None

class TestOptionalScreenMonitor:
    """Test version of optional screen monitor"""
    
    def __init__(self):
        self.monitoring_enabled = False
        self.monitoring_active = False
        
    def enable_monitoring(self, enabled: bool = True):
        """Test enable monitoring"""
        self.monitoring_enabled = enabled
        return f"Ekran izleme {'etkinleştirildi' if enabled else 'devre dışı bırakıldı'}"
    
    def start_monitoring(self):
        """Test start monitoring"""
        if not self.monitoring_enabled or not SCREEN_MONITORING_AVAILABLE:
            return False
        
        self.monitoring_active = True
        print("📺 Test screen monitoring started")
        return True
    
    def stop_monitoring(self):
        """Test stop monitoring"""
        self.monitoring_active = False
        print("📺 Test screen monitoring stopped")
    
    def get_current_screen_analysis(self) -> Dict[str, Any]:
        """Test screen analysis"""
        if not self.monitoring_active:
            return {
                'app_category': 'bilinmiyor',
                'productivity_score': 0,
                'monitoring_active': False
            }
        
        return {
            'app_category': 'test_app',
            'productivity_score': 85,
            'monitoring_active': True
        }

def run_tests():
    """Run comprehensive tests"""
    print("🧪 Starting Enhanced AI Coach Tests...")
    print("=" * 50)
    
    # Test 1: Initialize components
    print("\n1️⃣ Testing Component Initialization...")
    coach = TestEnhancedAICoach()
    screen_monitor = TestOptionalScreenMonitor()
    print("✅ Components initialized")
    
    # Test 2: Model loading (optional - requires model download)
    print("\n2️⃣ Testing Model Loading...")
    model_loaded = coach.load_model()
    if not model_loaded:
        print("⚠️ Skipping model-dependent tests")
    
    # Test 3: Screen monitoring
    print("\n3️⃣ Testing Optional Screen Monitoring...")
    print(screen_monitor.enable_monitoring(True))
    screen_monitor.start_monitoring()
    analysis = screen_monitor.get_current_screen_analysis()
    print(f"Screen analysis: {analysis}")
    screen_monitor.stop_monitoring()
    print(screen_monitor.enable_monitoring(False))
    
    # Test 4: Audio feedback
    print("\n4️⃣ Testing Audio Feedback...")
    test_text = "Merhaba! Bu bir test mesajıdır."
    audio_file = coach.test_audio_feedback(test_text)
    if audio_file:
        print(f"✅ Audio test completed: {audio_file}")
    
    # Test 5: Multimodal response (if model loaded)
    if model_loaded:
        print("\n5️⃣ Testing Multimodal Response...")
        test_context = {
            'duration': 15,
            'attention_score': 80,
            'face_detected': True
        }
        response = coach.test_multimodal_response("Çalışma başlangıcı", test_context)
        print(f"AI Response: {response}")
    
    print("\n" + "=" * 50)
    print("🎉 Tests completed!")
    print(f"📊 Results:")
    print(f"   - Audio Available: {AUDIO_AVAILABLE}")
    print(f"   - Screen Monitoring Available: {SCREEN_MONITORING_AVAILABLE}")
    print(f"   - Model Loaded: {model_loaded}")

if __name__ == "__main__":
    run_tests()
