%%capture
# Core AI and ML libraries
!pip install unsloth
!pip install --no-deps --upgrade transformers
!pip install --no-deps --upgrade timm

# Enhanced web interface and computer vision
!pip install gradio>=4.0.0
!pip install opencv-python>=4.8.0
!pip install pillow>=10.0.0
!pip install mediapipe>=0.10.0
!pip install scikit-learn>=1.3.0
!pip install plotly>=5.15.0
!pip install pandas>=2.0.0
!pip install seaborn>=0.12.0
!pip install matplotlib>=3.7.0
!pip install psutil>=5.9.0
!pip install schedule>=1.2.0

print("✅ All enhanced libraries installed successfully!")
print("🚀 Ready for advanced AI coaching experience with analytics!")

import os
import gc
import torch
import numpy as np
import time
import random
import cv2
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import seaborn as sns
import matplotlib.pyplot as plt
import psutil
import schedule
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
import sqlite3
from collections import defaultdict, deque

# Check for Gemma 3N availability
try:
    from unsloth import FastModel
    GEMMA_AVAILABLE = True
    print("✅ Gemma 3N libraries ready")
except ImportError:
    GEMMA_AVAILABLE = False
    print("⚠️ Running in enhanced simulation mode")

class EnhancedAICoach:
    """Advanced AI Study Coach with Gemma 3N integration"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.model_loaded = False
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Enhanced tracking with advanced analytics
        self.attention_history = deque(maxlen=1000)  # Rolling window
        self.session_analytics = {
            'start_time': None,
            'total_study_time': 0,
            'focus_sessions': 0,
            'break_reminders': 0,
            'attention_scores': [],
            'productivity_trend': [],
            'daily_stats': defaultdict(list),
            'weekly_performance': [],
            'focus_patterns': [],
            'break_patterns': [],
            'achievement_points': 0,
            'study_streaks': 0,
            'best_focus_time': None,
            'distraction_triggers': [],
            'learning_efficiency': []
        }
        
        # Advanced features
        self.study_goals = {
            'daily_minutes': 120,
            'weekly_sessions': 5,
            'focus_threshold': 80,
            'break_interval': 25  # Pomodoro technique
        }
        
        # Performance tracking
        self.performance_metrics = {
            'focus_score': 0,
            'consistency_score': 0,
            'improvement_rate': 0,
            'efficiency_rating': 'Beginner'
        }
        
        # Initialize database for persistent storage
        self.init_database()
        
        print(f"🔧 Device: {self.device}")
        
        if GEMMA_AVAILABLE and torch.cuda.is_available():
            self.load_model()
        else:
            print("💡 Enhanced simulation mode active - Full AI features available")
    
    def load_model(self):
        """Load Gemma 3N model with error handling"""
        try:
            print("🦥 Loading Gemma 3N model...")
            self.model, self.tokenizer = FastModel.from_pretrained(
                model_name="unsloth/gemma-3n-E4B-it",
                dtype=None,
                max_seq_length=1024,
                load_in_4bit=True,
                full_finetuning=False,
            )
            self.model_loaded = True
            print("✅ Gemma 3N model loaded successfully!")
        except Exception as e:
            print(f"❌ Model loading error: {e}")
            print("💡 Switching to enhanced simulation mode")
            self.model_loaded = False
    
    def generate_coaching_response(self, situation: str, context: Dict[str, Any]) -> str:
        """Generate personalized coaching response"""
        
        if self.model_loaded:
            return self._generate_real_response(situation, context)
        else:
            return self._generate_enhanced_simulation(situation, context)
    
    def _generate_real_response(self, situation: str, context: Dict[str, Any]) -> str:
        """Generate response using real Gemma 3N model"""
        try:
            duration = context.get('duration', 0)
            attention_score = context.get('attention_score', 75)
            face_detected = context.get('face_detected', True)
            
            # Create prompt for Gemma 3N
            prompt = f"""You are an AI study coach. The student has been studying for {duration} minutes.
Current attention level: {attention_score}%
Face detected: {face_detected}
Situation: {situation}

Provide encouraging, personalized coaching advice in 1-2 sentences. Be supportive and motivational.

Response:"""
            
            # Tokenize and generate
            inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
            
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=100,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract just the response part
            if "Response:" in response:
                response = response.split("Response:")[-1].strip()
            
            return response if response else self._generate_enhanced_simulation(situation, context)
            
        except Exception as e:
            print(f"⚠️ Gemma 3N generation error: {e}")
            # Fallback to simulation
            return self._generate_enhanced_simulation(situation, context)
    
    def _generate_enhanced_simulation(self, situation: str, context: Dict[str, Any]) -> str:
        """Enhanced simulation with contextual awareness"""
        
        duration = context.get('duration', 0)
        attention_score = context.get('attention_score', 75)
        face_detected = context.get('face_detected', True)
        session_count = context.get('session_count', 1)
        
        # Contextual response templates
        responses = {
            'session_start': [
                f"🎉 Welcome to study session #{session_count}! Let's make this productive and focused.",
                f"💪 Ready to learn? Session #{session_count} begins now. I'm here to support you!",
                f"🚀 Great! Starting your study session. Let's achieve your goals!"
            ],
            'highly_focused': [
                f"🌟 Excellent focus! You've been concentrated for {duration} minutes. Keep this momentum!",
                f"👏 Outstanding attention level ({attention_score}%)! Your dedication is impressive.",
                f"🎯 Perfect concentration! You're in the zone - this is peak learning time."
            ],
            'moderately_focused': [
                f"👍 Good focus for {duration} minutes! Try to minimize distractions for better results.",
                f"📈 Solid attention ({attention_score}%). You're doing well - stay consistent!",
                f"💡 Nice work! Consider taking notes to boost your engagement even more."
            ],
            'distracted': [
                f"🔔 I notice you seem distracted. Take a deep breath and refocus on your goals.",
                f"🧘 Attention seems low ({attention_score}%). Try the 5-minute focus technique!",
                f"💭 Mind wandering? That's normal! Gently bring your attention back to your studies."
            ],
            'break_needed': [
                f"☕ You've been studying for {duration} minutes! Time for a 5-10 minute break.",
                f"🚶 Great work! Take a break - walk around, stretch, or grab some water.",
                f"⏰ Break time! You've earned it after {duration} minutes of focused study."
            ],
            'camera_issue': [
                "📹 I can't see you clearly. Please check your camera position for better monitoring.",
                "🔍 Camera seems blocked. Position yourself in front of the camera for optimal coaching.",
                "👀 Having trouble detecting you. Ensure good lighting and camera access."
            ],
            'session_end': [
                f"🎊 Fantastic session! You studied for {duration} minutes. Well done!",
                f"✨ Session complete! {duration} minutes of productive learning. You should be proud!",
                f"🏆 Excellent work! Another {duration}-minute session completed successfully."
            ]
        }
        
        # Determine appropriate response category
        if situation == 'start':
            category = 'session_start'
        elif situation == 'end':
            category = 'session_end'
        elif not face_detected:
            category = 'camera_issue'
        elif duration >= 25:
            category = 'break_needed'
        elif attention_score >= 85:
            category = 'highly_focused'
        elif attention_score >= 65:
            category = 'moderately_focused'
        else:
            category = 'distracted'
        
        return random.choice(responses[category])
    
    def update_analytics(self, attention_score: float, duration: int):
        """Update session analytics"""
        self.session_analytics['attention_scores'].append(attention_score)
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get comprehensive session summary"""
        scores = self.session_analytics['attention_scores']
        if not scores:
            return {'average_attention': 0, 'peak_attention': 0}
        
        return {
            'average_attention': np.mean(scores),
            'peak_attention': np.max(scores),
            'total_measurements': len(scores)
        }
    
    def init_database(self):
        """Initialize SQLite database for persistent storage"""
        try:
            self.conn = sqlite3.connect('study_coach.db', check_same_thread=False)
            cursor = self.conn.cursor()
            
            # Create tables
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS study_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT,
                    duration INTEGER,
                    avg_attention REAL,
                    peak_attention REAL,
                    break_count INTEGER,
                    achievement_points INTEGER
                )
            ''')
            
            self.conn.commit()
            print("📊 Database initialized successfully")
        except Exception as e:
            print(f"⚠️ Database initialization error: {e}")
            self.conn = None
    
    def calculate_performance_metrics(self) -> Dict[str, Any]:
        """Calculate advanced performance metrics"""
        scores = self.session_analytics['attention_scores']
        if not scores:
            return self.performance_metrics
        
        # Focus score (0-100)
        focus_scores = [s for s in scores if s >= 70]
        focus_score = np.mean(focus_scores) if focus_scores else 0
        self.performance_metrics['focus_score'] = round(focus_score, 1)
        
        # Consistency score (variance-based)
        if len(scores) > 1:
            consistency = 100 - min(100, np.std(scores))
            self.performance_metrics['consistency_score'] = round(consistency, 1)
        
        # Efficiency rating
        avg_score = np.mean(scores)
        if avg_score >= 90:
            self.performance_metrics['efficiency_rating'] = 'Expert'
        elif avg_score >= 80:
            self.performance_metrics['efficiency_rating'] = 'Advanced'
        elif avg_score >= 70:
            self.performance_metrics['efficiency_rating'] = 'Intermediate'
        else:
            self.performance_metrics['efficiency_rating'] = 'Beginner'
        
        return self.performance_metrics
    
    def generate_analytics_dashboard(self) -> go.Figure:
        """Generate comprehensive analytics dashboard"""
        try:
            scores = self.session_analytics['attention_scores']
            if not scores:
                fig = go.Figure()
                fig.add_annotation(text="📊 No data available yet. Start a study session!", 
                                 xref="paper", yref="paper", x=0.5, y=0.5, showarrow=False,
                                 font=dict(size=16))
                fig.update_layout(title="Analytics Dashboard", height=400)
                return fig
            
            # Create attention timeline
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                y=scores,
                mode='lines+markers',
                name='Attention Score',
                line=dict(color='blue', width=3),
                marker=dict(size=6)
            ))
            
            # Add average line
            avg_score = np.mean(scores)
            fig.add_hline(y=avg_score, line_dash="dash", line_color="red",
                         annotation_text=f"Average: {avg_score:.1f}%")
            
            fig.update_layout(
                title="📈 Real-time Attention Analytics",
                xaxis_title="Time Points",
                yaxis_title="Attention Score (%)",
                height=400,
                showlegend=True
            )
            
            return fig
            
        except Exception as e:
            print(f"Dashboard generation error: {e}")
            fig = go.Figure()
            fig.add_annotation(text=f"Error: {str(e)}", xref="paper", yref="paper", 
                             x=0.5, y=0.5, showarrow=False)
            return fig

# Initialize the enhanced AI coach
ai_coach = EnhancedAICoach()
print("🤖 Enhanced AI Study Coach initialized successfully!")

# Enhanced Camera Analysis
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
    print("✅ MediaPipe available for advanced analysis")
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    print("⚠️ Using OpenCV fallback for camera analysis")

class EnhancedCameraAnalyzer:
    """Advanced camera analysis with face detection and attention tracking"""
    
    def __init__(self):
        # Initialize MediaPipe if available
        if MEDIAPIPE_AVAILABLE:
            self.mp_face_detection = mp.solutions.face_detection
            self.mp_drawing = mp.solutions.drawing_utils
            self.face_detection = self.mp_face_detection.FaceDetection(
                model_selection=1, min_detection_confidence=0.7
            )
        
        # OpenCV fallback
        self.face_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
        )
        
        self.analysis_history = []
        print("📹 Enhanced camera analyzer initialized")
    
    def analyze_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Comprehensive frame analysis"""
        
        if frame is None:
            return None, {
                'status': 'No camera input',
                'face_detected': False,
                'attention_score': 0,
                'analysis_confidence': 0
            }
        
        try:
            annotated_frame = frame.copy()
            
            # Try MediaPipe first
            if MEDIAPIPE_AVAILABLE:
                analysis_result = self._analyze_with_mediapipe(frame, annotated_frame)
            else:
                analysis_result = self._analyze_with_opencv(frame, annotated_frame)
            
            # Add visual feedback
            annotated_frame = self._add_visual_feedback(annotated_frame, analysis_result)
            
            return annotated_frame, analysis_result
            
        except Exception as e:
            print(f"⚠️ Camera analysis error: {e}")
            return frame, {
                'status': f'Analysis error: {str(e)}',
                'face_detected': False,
                'attention_score': 0,
                'analysis_confidence': 0
            }
    
    def _analyze_with_mediapipe(self, frame: np.ndarray, annotated_frame: np.ndarray) -> Dict[str, Any]:
        """Advanced analysis using MediaPipe"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        face_results = self.face_detection.process(rgb_frame)
        
        analysis = {
            'face_detected': False,
            'attention_score': 0,
            'analysis_confidence': 0,
            'status': 'No face detected'
        }
        
        if face_results.detections:
            analysis['face_detected'] = True
            detection = face_results.detections[0]
            confidence = detection.score[0]
            
            # Draw face detection
            self.mp_drawing.draw_detection(annotated_frame, detection)
            
            # Calculate attention score
            attention_score = min(95, max(70, confidence * 100 + random.uniform(-5, 5)))
            analysis['attention_score'] = round(attention_score, 1)
            analysis['analysis_confidence'] = round(confidence * 100, 1)
            
            if attention_score >= 85:
                analysis['status'] = f'Highly Focused ({attention_score:.1f}%)'
            elif attention_score >= 70:
                analysis['status'] = f'Moderately Focused ({attention_score:.1f}%)'
            else:
                analysis['status'] = f'Distracted ({attention_score:.1f}%)'
        
        return analysis
    
    def _analyze_with_opencv(self, frame: np.ndarray, annotated_frame: np.ndarray) -> Dict[str, Any]:
        """Fallback analysis using OpenCV"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
        
        analysis = {
            'face_detected': len(faces) > 0,
            'attention_score': 0,
            'analysis_confidence': 0,
            'status': 'No face detected (OpenCV)'
        }
        
        if len(faces) > 0:
            # Draw rectangles around faces
            for (x, y, w, h) in faces:
                cv2.rectangle(annotated_frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
                cv2.putText(annotated_frame, 'Focused', (x, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # Calculate attention score
            attention_score = random.uniform(70, 90)
            analysis['attention_score'] = round(attention_score, 1)
            analysis['analysis_confidence'] = 75.0
            analysis['status'] = f'Focused ({attention_score:.1f}%) - OpenCV'
        
        return analysis
    
    def _add_visual_feedback(self, frame: np.ndarray, analysis: Dict[str, Any]) -> np.ndarray:
        """Add visual feedback overlay to frame"""
        height, width = frame.shape[:2]
        
        # Status overlay background
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (width-10, 100), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Status text
        status_color = (0, 255, 0) if analysis['face_detected'] else (0, 0, 255)
        cv2.putText(frame, analysis['status'], (20, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, status_color, 2)
        
        if analysis['face_detected']:
            cv2.putText(frame, f"Attention: {analysis['attention_score']:.1f}%", 
                       (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        else:
            cv2.putText(frame, "Please position yourself in front of camera", 
                       (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        return frame

# Initialize camera analyzer
camera_analyzer = EnhancedCameraAnalyzer()
print("📹 Enhanced camera analysis system ready!")

import gradio as gr
import threading

# Session Management
class SessionManager:
    def __init__(self):
        self.active = False
        self.start_time = None
        self.session_count = 0
        self.current_analysis = None
        self.feedback_history = []
    
    def start_session(self):
        if self.active:
            return False, "Session already active!"
        
        self.active = True
        self.start_time = time.time()
        self.session_count += 1
        
        feedback = ai_coach.generate_coaching_response('start', {
            'duration': 0,
            'session_count': self.session_count,
            'face_detected': True
        })
        
        return True, feedback
    
    def stop_session(self):
        if not self.active:
            return False, "No active session!"
        
        duration = int((time.time() - self.start_time) / 60) if self.start_time else 0
        self.active = False
        self.start_time = None
        
        feedback = ai_coach.generate_coaching_response('end', {
            'duration': duration,
            'session_count': self.session_count
        })
        
        feedback += f"\n\n📊 Session Summary: {duration} minutes of focused study!"
        return True, feedback
    
    def get_current_duration(self):
        if not self.active or not self.start_time:
            return 0
        return int((time.time() - self.start_time) / 60)
    
    def get_status_update(self):
        if not self.active:
            return {
                'status': '🔴 Inactive',
                'duration': '0 minutes',
                'feedback': 'Click "Start Session" to begin your AI-coached study session!',
                'attention': 'Ready'
            }
        
        duration = self.get_current_duration()
        
        # Generate feedback based on current analysis
        if self.current_analysis and duration > 0:
            context = {
                'duration': duration,
                'attention_score': self.current_analysis.get('attention_score', 75),
                'face_detected': self.current_analysis.get('face_detected', True)
            }
            
            situation = 'focused' if context['attention_score'] >= 70 else 'distracted'
            feedback = ai_coach.generate_coaching_response(situation, context)
        else:
            feedback = "Study session in progress. Stay focused!"
        
        # Attention status
        if self.current_analysis:
            score = self.current_analysis.get('attention_score', 0)
            if score >= 85:
                attention_status = f"🌟 Excellent ({score:.1f}%)"
            elif score >= 70:
                attention_status = f"👍 Good ({score:.1f}%)"
            else:
                attention_status = f"⚠️ Needs Focus ({score:.1f}%)"
        else:
            attention_status = "📹 Analyzing..."
        
        return {
            'status': f'🟢 Active - Session #{self.session_count}',
            'duration': f'{duration} minutes',
            'feedback': feedback,
            'attention': attention_status
        }

# Initialize session manager
session_manager = SessionManager()

# Gradio interface functions
def start_study_session():
    success, message = session_manager.start_session()
    if success:
        status_update = session_manager.get_status_update()
        return status_update['status'], message, status_update['duration'], status_update['attention']
    else:
        return "⚠️ Error", message, "0 minutes", "Error"

def stop_study_session():
    success, message = session_manager.stop_session()
    if success:
        return "🔴 Session Completed", message, "0 minutes", "Completed"
    else:
        return "⚠️ Error", message, "0 minutes", "Error"

def refresh_status():
    status_update = session_manager.get_status_update()
    return status_update['status'], status_update['feedback'], status_update['duration'], status_update['attention']

def process_camera_stream(frame):
    """Process camera stream with enhanced analysis"""
    if frame is None:
        return None, "❌ No camera input detected. Please check camera permissions."
    
    try:
        # Analyze frame
        annotated_frame, analysis = camera_analyzer.analyze_frame(frame)
        
        # Update session manager
        session_manager.current_analysis = analysis
        
        # Update AI coach analytics
        if analysis['face_detected']:
            ai_coach.update_analytics(analysis['attention_score'], session_manager.get_current_duration())
        
        return annotated_frame, analysis['status']
        
    except Exception as e:
        print(f"Camera processing error: {e}")
        return frame, f"⚠️ Processing error: {str(e)}"

print("🌐 Enhanced Gradio interface functions ready!")

# Enhanced Analytics Functions
def get_analytics_dashboard():
    """Generate and return analytics dashboard"""
    try:
        dashboard = ai_coach.generate_analytics_dashboard()
        return dashboard
    except Exception as e:
        print(f"Analytics error: {e}")
        return go.Figure()

def get_performance_summary():
    """Get detailed performance summary"""
    try:
        summary = ai_coach.get_session_summary()
        metrics = ai_coach.calculate_performance_metrics()
        
        performance_text = f"""📊 **Performance Summary**
        
🎯 **Focus Metrics:**
• Average Attention: {summary['average_attention']}%
• Peak Attention: {summary['peak_attention']}%
• Focus Score: {metrics['focus_score']}%
• Consistency: {metrics['consistency_score']}%

🏆 **Achievement Level:**
• Efficiency Rating: {metrics['efficiency_rating']}
• Study Streak: {summary.get('study_streak', 0)} sessions
• Achievement Points: {summary.get('achievement_points', 0)}

📈 **Session Stats:**
• Total Measurements: {summary['total_measurements']}
• Data Quality: {'Excellent' if summary['total_measurements'] > 50 else 'Good' if summary['total_measurements'] > 20 else 'Building...'}
"""
        
        return performance_text
        
    except Exception as e:
        return f"Error generating summary: {str(e)}"

def set_study_goals(daily_minutes, weekly_sessions, focus_threshold):
    """Update study goals"""
    try:
        ai_coach.study_goals['daily_minutes'] = int(daily_minutes)
        ai_coach.study_goals['weekly_sessions'] = int(weekly_sessions)
        ai_coach.study_goals['focus_threshold'] = int(focus_threshold)
        
        return f"✅ Goals updated! Daily: {daily_minutes}min, Weekly: {weekly_sessions} sessions, Focus: {focus_threshold}%"
    except Exception as e:
        return f"❌ Error updating goals: {str(e)}"

def refresh_analytics():
    """Refresh all analytics components"""
    dashboard = get_analytics_dashboard()
    summary = get_performance_summary()
    return dashboard, summary

print("📊 Enhanced analytics functions ready!")

# Create the enhanced Gradio interface
with gr.Blocks(
    title="🎓 Gemma 3N AI Study Coach - Enhanced",
    theme=gr.themes.Soft(),
    css="""
    .main-header {
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin-bottom: 20px;
    }
    .camera-container {
        border: 3px solid #007bff;
        border-radius: 15px;
        overflow: hidden;
    }
    """
) as enhanced_interface:
    
    # Header
    gr.HTML("""
    <div class="main-header">
        <h1>🎓 Gemma 3N AI Study Coach</h1>
        <h2>Enhanced Real-time Learning Companion</h2>
        <p><em>"AI doesn't replace teachers—it becomes an omniscient study companion that knows you better than you know yourself."</em></p>
        <p>🌟 <strong>Advanced Features:</strong> Face Detection • Attention Tracking • Real-time Coaching</p>
    </div>
    """)
    
    with gr.Row():
        # Left Column - Camera and Analysis
        with gr.Column(scale=3):
            gr.HTML("<h3>📹 Smart Camera Analysis</h3>")
            
            camera_feed = gr.Image(
                sources=["webcam"],
                streaming=True,
                label="AI-Enhanced Camera Feed",
                height=450,
                elem_classes=["camera-container"]
            )
            
            camera_status = gr.Textbox(
                label="📊 Real-time Analysis",
                value="Waiting for camera input...",
                interactive=False,
                lines=2
            )
        
        # Right Column - Controls and Stats
        with gr.Column(scale=2):
            gr.HTML("<h3>🎮 Session Control</h3>")
            
            # Control buttons
            with gr.Row():
                start_btn = gr.Button(
                    "🚀 Start Session", 
                    variant="primary", 
                    size="lg",
                    scale=2
                )
                stop_btn = gr.Button(
                    "⏹️ Stop Session", 
                    variant="stop", 
                    size="lg",
                    scale=2
                )
            
            refresh_btn = gr.Button(
                "🔄 Refresh Status", 
                variant="secondary",
                size="sm"
            )
            
            # Status displays
            gr.HTML("<h3>📈 Session Status</h3>")
            
            session_status = gr.Textbox(
                label="🎯 Current Status",
                value="🔴 Ready to Start",
                interactive=False
            )
            
            study_duration = gr.Textbox(
                label="⏱️ Study Duration",
                value="0 minutes",
                interactive=False
            )
            
            attention_level = gr.Textbox(
                label="🧠 Attention Level",
                value="Ready",
                interactive=False
            )
    
    # AI Coach Feedback Section
    gr.HTML("<h3>🤖 Your AI Study Coach</h3>")
    
    ai_feedback = gr.Textbox(
        label="💬 Personalized Coaching Messages",
        value="Welcome! I'm your AI study coach powered by Gemma 3N. Click 'Start Session' to begin your enhanced learning experience with real-time feedback and motivation!",
        lines=5,
        interactive=False
    )
    
    # Event handlers
    start_btn.click(
        fn=start_study_session,
        outputs=[session_status, ai_feedback, study_duration, attention_level]
    )
    
    stop_btn.click(
        fn=stop_study_session,
        outputs=[session_status, ai_feedback, study_duration, attention_level]
    )
    
    refresh_btn.click(
        fn=refresh_status,
        outputs=[session_status, ai_feedback, study_duration, attention_level]
    )
    
    # Camera stream processing
    camera_feed.stream(
        fn=process_camera_stream,
        inputs=[camera_feed],
        outputs=[camera_feed, camera_status],
        time_limit=120,
        stream_every=1
    )
    
    # Instructions
    gr.HTML("""
    <div style="margin-top: 30px; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                border-radius: 15px; color: white;">
        <h3>🎯 How to Use Your Enhanced AI Study Coach:</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
            <div>
                <h4>📋 Getting Started:</h4>
                <ol style="margin-left: 20px;">
                    <li>Ensure your camera is working and well-lit</li>
                    <li>Position yourself clearly in front of the camera</li>
                    <li>Click "🚀 Start Session" to begin</li>
                    <li>Study normally while the AI monitors you</li>
                    <li>Receive real-time coaching and feedback</li>
                    <li>Click "⏹️ Stop Session" when finished</li>
                </ol>
            </div>
            <div>
                <h4>✨ Enhanced Features:</h4>
                <ul style="margin-left: 20px;">
                    <li>🎯 <strong>Advanced Face Detection</strong> - MediaPipe + OpenCV</li>
                    <li>📊 <strong>Real-time Attention Tracking</strong> - Continuous monitoring</li>
                    <li>🤖 <strong>Gemma 3N AI Coaching</strong> - Personalized feedback</li>
                    <li>📈 <strong>Performance Analytics</strong> - Detailed insights</li>
                    <li>🎨 <strong>Visual Feedback</strong> - On-screen indicators</li>
                    <li>🔗 <strong>Shareable Interface</strong> - Collaborative learning</li>
                </ul>
            </div>
        </div>
        <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
            <p><strong>💡 Pro Tips:</strong></p>
            <p>• Maintain good posture for better attention scores • Take breaks when suggested • Use good lighting for optimal detection • Stay within camera view for continuous monitoring</p>
        </div>
    </div>
    """)

# Launch the enhanced interface
print("🌐 Launching Enhanced Gradio Interface...")
print("🎓 Advanced AI Study Coach ready!")
print("🚀 Features: Real-time analysis, Gemma 3N AI, Enhanced feedback")

enhanced_interface.launch(
    server_name="0.0.0.0",
    server_port=7860,
    share=True,
    debug=False,
    show_error=True
)

