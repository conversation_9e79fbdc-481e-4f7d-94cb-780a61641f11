{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 🎓 Enhanced Gemma 3N AI Study Coach\n",
    "\n",
    "**Advanced AI-powered study coaching system with real-time analysis**\n",
    "\n",
    "## Features:\n",
    "- 🌐 **Modern Gradio Web Interface** - Professional, responsive design\n",
    "- 🤖 **Enhanced Gemma 3N AI** - Advanced coaching with contextual awareness\n",
    "- 📹 **Smart Camera Analysis** - Face detection, attention tracking, posture analysis\n",
    "- 📊 **Real-time Analytics** - Performance metrics, focus patterns, productivity insights\n",
    "- 🎯 **Intelligent Feedback** - Personalized coaching based on behavior analysis\n",
    "- 🔗 **Shareable Interface** - Collaborative learning with public links\n",
    "\n",
    "## System Requirements:\n",
    "- Kaggle Notebook with GPU enabled\n",
    "- Internet connection enabled\n",
    "- Modern web browser with camera access"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📦 Install Enhanced Dependencies"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 1,
   "metadata": {},
   "outputs": [],
   "source": [
    "%%capture\n",
    "# Core AI and ML libraries\n",
    "!pip install unsloth\n",
    "!pip install --no-deps --upgrade transformers\n",
    "!pip install --no-deps --upgrade timm\n",
    "\n",
    "# Enhanced web interface and computer vision\n",
    "!pip install gradio>=4.0.0\n",
    "!pip install opencv-python>=4.8.0\n",
    "!pip install pillow>=10.0.0\n",
    "!pip install mediapipe>=0.10.0\n",
    "!pip install scikit-learn>=1.3.0\n",
    "!pip install plotly>=5.15.0\n",
    "!pip install pandas>=2.0.0\n",
    "!pip install seaborn>=0.12.0\n",
    "!pip install matplotlib>=3.7.0\n",
    "!pip install psutil>=5.9.0\n",
    "!pip install schedule>=1.2.0\n",
    "!pip install pyautogui>=0.9.54\n",
    "!pip install pygetwindow>=0.0.9\n",
    "!pip install pytesseract>=0.3.10\n",
    "!pip install Pillow>=10.0.0\n",
    "!pip install pywin32>=306  # Windows specific\n",
    "!pip install screeninfo>=0.8.1\n",
    "\n",
    "print(\"✅ All enhanced libraries installed successfully!\")\n",
    "print(\"🚀 Ready for advanced AI coaching experience with analytics!\")\n",
    "print(\"📺 Screen monitoring capabilities enabled!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🤖 Enhanced AI Coach System"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 2,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n"
     ]
    },
    {
     "name": "stderr",
     "output_type": "stream",
     "text": [
      "2025-08-02 22:19:59.072137: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:477] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n",
      "WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n",
      "E0000 00:00:1754173199.301940      90 cuda_dnn.cc:8310] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n",
      "E0000 00:00:1754173199.368116      90 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n"
     ]
    },
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "🦥 Unsloth Zoo will now patch everything to make training faster!\n",
      "✅ Gemma 3N libraries ready\n",
      "🔧 Device: cuda\n",
      "🦥 Loading Gemma 3N model...\n",
      "==((====))==  Unsloth 2025.8.1: Fast Gemma3N patching. Transformers: 4.54.1.\n",
      "   \\\\   /|    Tesla T4. Num GPUs = 2. Max memory: 14.741 GB. Platform: Linux.\n",
      "O^O/ \\_/ \\    Torch: 2.7.1+cu126. CUDA: 7.5. CUDA Toolkit: 12.6. Triton: 3.3.1\n",
      "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.31.post1. FA2 = False]\n",
      " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n",
      "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n",
      "Unsloth: Gemma3N does not support SDPA - switching to eager!\n"
     ]
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "918e0a3cceaf4ddd93c6b816bb0998d5",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "model.safetensors.index.json: 0.00B [00:00, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "96fda437a91d4f109d5b0c09b8160951",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "model-00001-of-00003.safetensors:   0%|          | 0.00/3.72G [00:00<?, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "0cd9c5e6cff04e7b8bc9ada70827edb0",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "model-00002-of-00003.safetensors:   0%|          | 0.00/4.99G [00:00<?, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "e77d95e93bbe4d1e9492b57f9bdb987d",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "model-00003-of-00003.safetensors:   0%|          | 0.00/1.15G [00:00<?, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "8ea6d6102fb941018e756e6d4e5c1b96",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "2b72c14ac1774d3990bec1c80dc4a81a",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "generation_config.json:   0%|          | 0.00/210 [00:00<?, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "cbadf8d3e5604623bfa42e35eaab8731",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "processor_config.json:   0%|          | 0.00/98.0 [00:00<?, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "8f4521270c1244ec858af594faa481d4",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "chat_template.jinja: 0.00B [00:00, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "48d81f28263b473fbaee30b3bbddde44",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "preprocessor_config.json: 0.00B [00:00, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "babdddcefc0246fa97301f97f6500fb0",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "tokenizer_config.json: 0.00B [00:00, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "70f4fe31439d4d099002bb563bc01016",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "tokenizer.model:   0%|          | 0.00/4.70M [00:00<?, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "0dd505927bfd442ba200c1d2dad04cd2",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "tokenizer.json:   0%|          | 0.00/33.4M [00:00<?, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "data": {
      "application/vnd.jupyter.widget-view+json": {
       "model_id": "baa0eb9666754ec89c227e4244a7ba4c",
       "version_major": 2,
       "version_minor": 0
      },
      "text/plain": [
       "special_tokens_map.json:   0%|          | 0.00/777 [00:00<?, ?B/s]"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    },
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "✅ Gemma 3N model loaded successfully!\n",
      "🤖 Enhanced AI Study Coach initialized successfully!\n"
     ]
    }
   ],
   "source": [
    "import os\n",
    "import gc\n",
    "import torch\n",
    "import numpy as np\n",
    "import time\n",
    "import random\n",
    "import cv2\n",
    "import pandas as pd\n",
    "import plotly.graph_objects as go\n",
    "import plotly.express as px\n",
    "from plotly.subplots import make_subplots\n",
    "import seaborn as sns\n",
    "import matplotlib.pyplot as plt\n",
    "import psutil\n",
    "import schedule\n",
    "from typing import List, Dict, Any, Optional, Tuple\n",
    "from datetime import datetime, timedelta\n",
    "import json\n",
    "import sqlite3\n",
    "from collections import defaultdict, deque\n",
    "import base64\n",
    "import io\n",
    "from PIL import Image\n",
    "\n",
    "# Audio processing imports\n",
    "try:\n",
    "    import pyttsx3\n",
    "    import speech_recognition as sr\n",
    "    import sounddevice as sd\n",
    "    import soundfile as sf\n",
    "    import librosa\n",
    "    AUDIO_AVAILABLE = True\n",
    "    print(\"✅ Audio processing libraries ready\")\n",
    "except ImportError:\n",
    "    AUDIO_AVAILABLE = False\n",
    "    print(\"⚠️ Audio features disabled - install pyttsx3, speechrecognition, sounddevice\")\n",
    "\n",
    "# Screen monitoring imports (optional)\n",
    "try:\n",
    "    import pyautogui\n",
    "    import pygetwindow as gw\n",
    "    import pytesseract\n",
    "    import win32gui\n",
    "    import win32process\n",
    "    from screeninfo import get_monitors\n",
    "    SCREEN_MONITORING_AVAILABLE = True\n",
    "    print(\"✅ Screen monitoring libraries ready\")\n",
    "except ImportError:\n",
    "    SCREEN_MONITORING_AVAILABLE = False\n",
    "    print(\"⚠️ Screen monitoring disabled - install pyautogui, pygetwindow, pytesseract\")\n",
    "\n",
    "# Computer vision imports\n",
    "try:\n",
    "    import mediapipe as mp\n",
    "    MEDIAPIPE_AVAILABLE = True\n",
    "    print(\"✅ MediaPipe ready\")\n",
    "except ImportError:\n",
    "    MEDIAPIPE_AVAILABLE = False\n",
    "    print(\"⚠️ MediaPipe disabled - install mediapipe\")\n",
    "\n",
    "# Check for Gemma 3N availability\n",
    "try:\n",
    "    from unsloth import FastModel\n",
    "    GEMMA_AVAILABLE = True\n",
    "    print(\"✅ Gemma 3N libraries ready\")\n",
    "except ImportError:\n",
    "    GEMMA_AVAILABLE = False\n",
    "    print(\"⚠️ Running in enhanced simulation mode\")\n",
    "\n",
    "class EnhancedAICoach:\n",
    "    \"\"\"Advanced AI Study Coach with Gemma 3N integration\"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        self.model = None\n",
    "        self.tokenizer = None\n",
    "        self.model_loaded = False\n",
    "        self.device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n",
    "        \n",
    "        # Enhanced tracking with advanced analytics\n",
    "        self.attention_history = deque(maxlen=1000)  # Rolling window\n",
    "        self.session_analytics = {\n",
    "            'start_time': None,\n",
    "            'total_study_time': 0,\n",
    "            'focus_sessions': 0,\n",
    "            'break_reminders': 0,\n",
    "            'attention_scores': [],\n",
    "            'productivity_trend': [],\n",
    "            'daily_stats': defaultdict(list),\n",
    "            'weekly_performance': [],\n",
    "            'focus_patterns': [],\n",
    "            'break_patterns': [],\n",
    "            'achievement_points': 0,\n",
    "            'study_streaks': 0,\n",
    "            'best_focus_time': None,\n",
    "            'distraction_triggers': [],\n",
    "            'learning_efficiency': []\n",
    "        }\n",
    "        \n",
    "        # Advanced features\n",
    "        self.study_goals = {\n",
    "            'daily_minutes': 120,\n",
    "            'weekly_sessions': 5,\n",
    "            'focus_threshold': 80,\n",
    "            'break_interval': 25  # Pomodoro technique\n",
    "        }\n",
    "        \n",
    "        # Performance tracking\n",
    "        self.performance_metrics = {\n",
    "            'focus_score': 0,\n",
    "            'consistency_score': 0,\n",
    "            'improvement_rate': 0,\n",
    "            'efficiency_rating': 'Beginner'\n",
    "        }\n",
    "        \n",
    "        # Initialize database for persistent storage\n",
    "        self.init_database()\n",
    "        \n",
    "        print(f\"🔧 Device: {self.device}\")\n",
    "        \n",
    "        if GEMMA_AVAILABLE and torch.cuda.is_available():\n",
    "            self.load_model()\n",
    "        else:\n",
    "            print(\"💡 Enhanced simulation mode active - Full AI features available\")\n",
    "    \n",
    "    def load_model(self):\n",
    "        \"\"\"Load Gemma 3N model with error handling\"\"\"\n",
    "        try:\n",
    "            print(\"🦥 Loading Gemma 3N model...\")\n",
    "            self.model, self.tokenizer = FastModel.from_pretrained(\n",
    "                model_name=\"unsloth/gemma-3n-E4B-it\",\n",
    "                dtype=None,\n",
    "                max_seq_length=1024,\n",
    "                load_in_4bit=True,\n",
    "                full_finetuning=False,\n",
    "            )\n",
    "            self.model_loaded = True\n",
    "            print(\"✅ Gemma 3N model loaded successfully!\")\n",
    "        except Exception as e:\n",
    "            print(f\"❌ Model loading error: {e}\")\n",
    "            print(\"💡 Switching to enhanced simulation mode\")\n",
    "            self.model_loaded = False\n",
    "    \n",
    "    def generate_coaching_response(self, situation: str, context: Dict[str, Any]) -> str:\n",
    "        \"\"\"Generate personalized coaching response with multimodal analysis\"\"\"\n",
    "        \n",
    "        # Enhance context with screen analysis if available\n",
    "        enhanced_context = self._enhance_context_with_screen_data(context)\n",
    "        \n",
    "        if self.model_loaded:\n",
    "            return self._generate_real_response(situation, enhanced_context)\n",
    "        else:\n",
    "            return self._generate_enhanced_simulation(situation, enhanced_context)\n",
    "    \n",
    "    def _enhance_context_with_screen_data(self, context: Dict[str, Any]) -> Dict[str, Any]:\n",
    "        \"\"\"Enhance context with screen monitoring data\"\"\"\n",
    "        enhanced_context = context.copy()\n",
    "        \n",
    "        try:\n",
    "            # Get screen analysis from global screen monitor\n",
    "            screen_analysis = screen_monitor.get_current_screen_analysis()\n",
    "            productivity_summary = screen_monitor.get_productivity_summary()\n",
    "            \n",
    "            enhanced_context.update({\n",
    "                'screen_monitoring_active': screen_monitor.monitoring_active,\n",
    "                'current_app_category': screen_analysis.get('app_category', 'unknown'),\n",
    "                'productivity_score': screen_analysis.get('productivity_score', 0),\n",
    "                'screen_recommendation': screen_analysis.get('recommendation', ''),\n",
    "                'average_productivity': productivity_summary.get('average_productivity', 0),\n",
    "                'distraction_count': productivity_summary.get('distraction_count', 0),\n",
    "                'screen_changes': productivity_summary.get('screen_changes', 0)\n",
    "            })\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"⚠️ Error enhancing context with screen data: {e}\")\n",
    "            enhanced_context['screen_monitoring_active'] = False\n",
    "        \n",
    "        return enhanced_context\n",
    "    \n",
    "    def _generate_real_response(self, situation: str, context: Dict[str, Any]) -> str:\n",
    "        \"\"\"Generate response using real Gemma 3N model with multimodal context\"\"\"\n",
    "        try:\n",
    "            duration = context.get('duration', 0)\n",
    "            attention_score = context.get('attention_score', 75)\n",
    "            face_detected = context.get('face_detected', True)\n",
    "            \n",
    "            # Enhanced context with screen monitoring\n",
    "            screen_active = context.get('screen_monitoring_active', False)\n",
    "            app_category = context.get('current_app_category', 'unknown')\n",
    "            productivity_score = context.get('productivity_score', 0)\n",
    "            distraction_count = context.get('distraction_count', 0)\n",
    "            screen_changes = context.get('screen_changes', 0)\n",
    "            \n",
    "            # Create enhanced prompt for Gemma 3N with multimodal context\n",
    "            prompt = f\"\"\"You are an advanced AI study coach with multimodal awareness. \n",
    "\n",
    "STUDENT STATUS:\n",
    "- Study duration: {duration} minutes\n",
    "- Camera attention level: {attention_score}%\n",
    "- Face detected: {face_detected}\n",
    "- Current situation: {situation}\n",
    "\n",
    "SCREEN ACTIVITY ANALYSIS:\n",
    "- Screen monitoring: {'Active' if screen_active else 'Inactive'}\n",
    "- Current application category: {app_category}\n",
    "- Productivity score: {productivity_score}%\n",
    "- Distractions detected: {distraction_count}\n",
    "- Screen changes: {screen_changes}\n",
    "\n",
    "COACHING INSTRUCTIONS:\n",
    "Provide personalized, actionable coaching advice (2-3 sentences) that considers both visual behavior and digital activity. \n",
    "Be encouraging but specific about improvements. Address both focus and productivity patterns.\n",
    "\n",
    "Response:\"\"\"\n",
    "            \n",
    "            # Tokenize and generate\n",
    "            inputs = self.tokenizer(prompt, return_tensors=\"pt\").to(self.device)\n",
    "            \n",
    "            with torch.no_grad():\n",
    "                outputs = self.model.generate(\n",
    "                    **inputs,\n",
    "                    max_new_tokens=100,\n",
    "                    temperature=0.7,\n",
    "                    do_sample=True,\n",
    "                    pad_token_id=self.tokenizer.eos_token_id\n",
    "                )\n",
    "            \n",
    "            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)\n",
    "            \n",
    "            # Extract just the response part\n",
    "            if \"Response:\" in response:\n",
    "                response = response.split(\"Response:\")[-1].strip()\n",
    "            \n",
    "            return response if response else self._generate_enhanced_simulation(situation, context)\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"⚠️ Gemma 3N generation error: {e}\")\n",
    "            # Fallback to simulation\n",
    "            return self._generate_enhanced_simulation(situation, context)\n",
    "    \n",
    "    def generate_audio_feedback(self, text: str) -> Optional[str]:\n",
    "        \"\"\"Generate audio feedback using text-to-speech\"\"\"\n",
    "        if not AUDIO_AVAILABLE:\n",
    "            return None\n",
    "        \n",
    "        try:\n",
    "            # Initialize TTS engine\n",
    "            engine = pyttsx3.init()\n",
    "            \n",
    "            # Configure voice settings\n",
    "            voices = engine.getProperty('voices')\n",
    "            if voices:\n",
    "                # Try to find a Turkish voice or use default\n",
    "                for voice in voices:\n",
    "                    if 'turkish' in voice.name.lower() or 'tr' in voice.id.lower():\n",
    "                        engine.setProperty('voice', voice.id)\n",
    "                        break\n",
    "            \n",
    "            # Set speech rate and volume\n",
    "            engine.setProperty('rate', 150)  # Slower for better understanding\n",
    "            engine.setProperty('volume', 0.8)\n",
    "            \n",
    "            # Generate audio file\n",
    "            audio_file = f\"feedback_{int(time.time())}.wav\"\n",
    "            engine.save_to_file(text, audio_file)\n",
    "            engine.runAndWait()\n",
    "            \n",
    "            return audio_file\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"⚠️ Audio generation error: {e}\")\n",
    "            return None\n",
    "    \n",
    "    def process_audio_input(self, audio_data: np.ndarray, sample_rate: int = 16000) -> Dict[str, Any]:\n",
    "        \"\"\"Process audio input for multimodal analysis\"\"\"\n",
    "        if not AUDIO_AVAILABLE:\n",
    "            return {'audio_processed': False}\n",
    "        \n",
    "        try:\n",
    "            # Basic audio analysis\n",
    "            audio_features = {\n",
    "                'duration': len(audio_data) / sample_rate,\n",
    "                'rms_energy': np.sqrt(np.mean(audio_data**2)),\n",
    "                'zero_crossing_rate': np.mean(librosa.feature.zero_crossing_rate(audio_data)),\n",
    "                'audio_processed': True\n",
    "            }\n",
    "            \n",
    "            # Speech recognition (optional)\n",
    "            try:\n",
    "                r = sr.Recognizer()\n",
    "                # Convert numpy array to audio data\n",
    "                audio_bytes = io.BytesIO()\n",
    "                sf.write(audio_bytes, audio_data, sample_rate, format='WAV')\n",
    "                audio_bytes.seek(0)\n",
    "                \n",
    "                with sr.AudioFile(audio_bytes) as source:\n",
    "                    audio = r.record(source)\n",
    "                    text = r.recognize_google(audio, language='tr-TR')\n",
    "                    audio_features['transcription'] = text\n",
    "            except:\n",
    "                audio_features['transcription'] = None\n",
    "            \n",
    "            return audio_features\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"⚠️ Audio processing error: {e}\")\n",
    "            return {'audio_processed': False}\n",
    "    \n",
    "    def _generate_enhanced_simulation(self, situation: str, context: Dict[str, Any]) -> str:\n",
    "        \"\"\"Enhanced simulation with contextual awareness\"\"\"\n",
    "        \n",
    "        duration = context.get('duration', 0)\n",
    "        attention_score = context.get('attention_score', 75)\n",
    "        face_detected = context.get('face_detected', True)\n",
    "        session_count = context.get('session_count', 1)\n",
    "        \n",
    "        # Contextual response templates\n",
    "        responses = {\n",
    "            'session_start': [\n",
    "                f\"🎉 Welcome to study session #{session_count}! Let's make this productive and focused.\",\n",
    "                f\"💪 Ready to learn? Session #{session_count} begins now. I'm here to support you!\",\n",
    "                f\"🚀 Great! Starting your study session. Let's achieve your goals!\"\n",
    "            ],\n",
    "            'highly_focused': [\n",
    "                f\"🌟 Excellent focus! You've been concentrated for {duration} minutes. Keep this momentum!\",\n",
    "                f\"👏 Outstanding attention level ({attention_score}%)! Your dedication is impressive.\",\n",
    "                f\"🎯 Perfect concentration! You're in the zone - this is peak learning time.\"\n",
    "            ],\n",
    "            'moderately_focused': [\n",
    "                f\"👍 Good focus for {duration} minutes! Try to minimize distractions for better results.\",\n",
    "                f\"📈 Solid attention ({attention_score}%). You're doing well - stay consistent!\",\n",
    "                f\"💡 Nice work! Consider taking notes to boost your engagement even more.\"\n",
    "            ],\n",
    "            'distracted': [\n",
    "                f\"🔔 I notice you seem distracted. Take a deep breath and refocus on your goals.\",\n",
    "                f\"🧘 Attention seems low ({attention_score}%). Try the 5-minute focus technique!\",\n",
    "                f\"💭 Mind wandering? That's normal! Gently bring your attention back to your studies.\"\n",
    "            ],\n",
    "            'break_needed': [\n",
    "                f\"☕ You've been studying for {duration} minutes! Time for a 5-10 minute break.\",\n",
    "                f\"🚶 Great work! Take a break - walk around, stretch, or grab some water.\",\n",
    "                f\"⏰ Break time! You've earned it after {duration} minutes of focused study.\"\n",
    "            ],\n",
    "            'camera_issue': [\n",
    "                \"📹 I can't see you clearly. Please check your camera position for better monitoring.\",\n",
    "                \"🔍 Camera seems blocked. Position yourself in front of the camera for optimal coaching.\",\n",
    "                \"👀 Having trouble detecting you. Ensure good lighting and camera access.\"\n",
    "            ],\n",
    "            'session_end': [\n",
    "                f\"🎊 Fantastic session! You studied for {duration} minutes. Well done!\",\n",
    "                f\"✨ Session complete! {duration} minutes of productive learning. You should be proud!\",\n",
    "                f\"🏆 Excellent work! Another {duration}-minute session completed successfully.\"\n",
    "            ]\n",
    "        }\n",
    "        \n",
    "        # Determine appropriate response category\n",
    "        if situation == 'start':\n",
    "            category = 'session_start'\n",
    "        elif situation == 'end':\n",
    "            category = 'session_end'\n",
    "        elif not face_detected:\n",
    "            category = 'camera_issue'\n",
    "        elif duration >= 25:\n",
    "            category = 'break_needed'\n",
    "        elif attention_score >= 85:\n",
    "            category = 'highly_focused'\n",
    "        elif attention_score >= 65:\n",
    "            category = 'moderately_focused'\n",
    "        else:\n",
    "            category = 'distracted'\n",
    "        \n",
    "        return random.choice(responses[category])\n",
    "    \n",
    "    def update_analytics(self, attention_score: float, duration: int):\n",
    "        \"\"\"Update session analytics\"\"\"\n",
    "        self.session_analytics['attention_scores'].append(attention_score)\n",
    "    \n",
    "    def get_session_summary(self) -> Dict[str, Any]:\n",
    "        \"\"\"Get comprehensive session summary\"\"\"\n",
    "        scores = self.session_analytics['attention_scores']\n",
    "        if not scores:\n",
    "            return {'average_attention': 0, 'peak_attention': 0}\n",
    "        \n",
    "        return {\n",
    "            'average_attention': np.mean(scores),\n",
    "            'peak_attention': np.max(scores),\n",
    "            'total_measurements': len(scores)\n",
    "        }\n",
    "    \n",
    "    def init_database(self):\n",
    "        \"\"\"Initialize SQLite database for persistent storage\"\"\"\n",
    "        try:\n",
    "            self.conn = sqlite3.connect('study_coach.db', check_same_thread=False)\n",
    "            cursor = self.conn.cursor()\n",
    "            \n",
    "            # Create tables\n",
    "            cursor.execute('''\n",
    "                CREATE TABLE IF NOT EXISTS study_sessions (\n",
    "                    id INTEGER PRIMARY KEY AUTOINCREMENT,\n",
    "                    date TEXT,\n",
    "                    duration INTEGER,\n",
    "                    avg_attention REAL,\n",
    "                    peak_attention REAL,\n",
    "                    break_count INTEGER,\n",
    "                    achievement_points INTEGER\n",
    "                )\n",
    "            ''')\n",
    "            \n",
    "            self.conn.commit()\n",
    "            print(\"📊 Database initialized successfully\")\n",
    "        except Exception as e:\n",
    "            print(f\"⚠️ Database initialization error: {e}\")\n",
    "            self.conn = None\n",
    "    \n",
    "    def calculate_performance_metrics(self) -> Dict[str, Any]:\n",
    "        \"\"\"Calculate advanced performance metrics\"\"\"\n",
    "        scores = self.session_analytics['attention_scores']\n",
    "        if not scores:\n",
    "            return self.performance_metrics\n",
    "        \n",
    "        # Focus score (0-100)\n",
    "        focus_scores = [s for s in scores if s >= 70]\n",
    "        focus_score = np.mean(focus_scores) if focus_scores else 0\n",
    "        self.performance_metrics['focus_score'] = round(focus_score, 1)\n",
    "        \n",
    "        # Consistency score (variance-based)\n",
    "        if len(scores) > 1:\n",
    "            consistency = 100 - min(100, np.std(scores))\n",
    "            self.performance_metrics['consistency_score'] = round(consistency, 1)\n",
    "        \n",
    "        # Efficiency rating\n",
    "        avg_score = np.mean(scores)\n",
    "        if avg_score >= 90:\n",
    "            self.performance_metrics['efficiency_rating'] = 'Expert'\n",
    "        elif avg_score >= 80:\n",
    "            self.performance_metrics['efficiency_rating'] = 'Advanced'\n",
    "        elif avg_score >= 70:\n",
    "            self.performance_metrics['efficiency_rating'] = 'Intermediate'\n",
    "        else:\n",
    "            self.performance_metrics['efficiency_rating'] = 'Beginner'\n",
    "        \n",
    "        return self.performance_metrics\n",
    "    \n",
    "    def generate_analytics_dashboard(self) -> go.Figure:\n",
    "        \"\"\"Generate comprehensive analytics dashboard\"\"\"\n",
    "        try:\n",
    "            scores = self.session_analytics['attention_scores']\n",
    "            if not scores:\n",
    "                fig = go.Figure()\n",
    "                fig.add_annotation(text=\"📊 No data available yet. Start a study session!\", \n",
    "                                 xref=\"paper\", yref=\"paper\", x=0.5, y=0.5, showarrow=False,\n",
    "                                 font=dict(size=16))\n",
    "                fig.update_layout(title=\"Analytics Dashboard\", height=400)\n",
    "                return fig\n",
    "            \n",
    "            # Create attention timeline\n",
    "            fig = go.Figure()\n",
    "            \n",
    "            fig.add_trace(go.Scatter(\n",
    "                y=scores,\n",
    "                mode='lines+markers',\n",
    "                name='Attention Score',\n",
    "                line=dict(color='blue', width=3),\n",
    "                marker=dict(size=6)\n",
    "            ))\n",
    "            \n",
    "            # Add average line\n",
    "            avg_score = np.mean(scores)\n",
    "            fig.add_hline(y=avg_score, line_dash=\"dash\", line_color=\"red\",\n",
    "                         annotation_text=f\"Average: {avg_score:.1f}%\")\n",
    "            \n",
    "            fig.update_layout(\n",
    "                title=\"📈 Real-time Attention Analytics\",\n",
    "                xaxis_title=\"Time Points\",\n",
    "                yaxis_title=\"Attention Score (%)\",\n",
    "                height=400,\n",
    "                showlegend=True\n",
    "            )\n",
    "            \n",
    "            return fig\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"Dashboard generation error: {e}\")\n",
    "            fig = go.Figure()\n",
    "            fig.add_annotation(text=f\"Error: {str(e)}\", xref=\"paper\", yref=\"paper\", \n",
    "                             x=0.5, y=0.5, showarrow=False)\n",
    "            return fig\n",
    "\n",
    "class OptionalScreenMonitor:\n",
    "    \"\"\"Optional screen monitoring with user control\"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        self.monitoring_enabled = False\n",
    "        self.monitoring_active = False\n",
    "        self.screen_history = deque(maxlen=100)\n",
    "        self.monitor_thread = None\n",
    "        \n",
    "        # Productivity categories\n",
    "        self.productivity_apps = {\n",
    "            'study': ['code', 'jupyter', 'notebook', 'pdf', 'reader', 'book', 'study'],\n",
    "            'productive': ['word', 'excel', 'powerpoint', 'docs', 'sheets', 'slides'],\n",
    "            'research': ['browser', 'chrome', 'firefox', 'edge', 'research', 'wikipedia'],\n",
    "            'distraction': ['game', 'social', 'facebook', 'youtube', 'instagram', 'tiktok']\n",
    "        }\n",
    "    \n",
    "    def enable_monitoring(self, enabled: bool = True):\n",
    "        \"\"\"Enable or disable screen monitoring\"\"\"\n",
    "        self.monitoring_enabled = enabled\n",
    "        if not enabled and self.monitoring_active:\n",
    "            self.stop_monitoring()\n",
    "        return f\"Ekran izleme {'etkinleştirildi' if enabled else 'devre dışı bırakıldı'}\"\n",
    "    \n",
    "    def start_monitoring(self):\n",
    "        \"\"\"Start screen monitoring if enabled\"\"\"\n",
    "        if not self.monitoring_enabled or not SCREEN_MONITORING_AVAILABLE:\n",
    "            return False\n",
    "        \n",
    "        if not self.monitoring_active:\n",
    "            self.monitoring_active = True\n",
    "            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)\n",
    "            self.monitor_thread.start()\n",
    "        return True\n",
    "    \n",
    "    def stop_monitoring(self):\n",
    "        \"\"\"Stop screen monitoring\"\"\"\n",
    "        self.monitoring_active = False\n",
    "        if self.monitor_thread:\n",
    "            self.monitor_thread.join(timeout=1)\n",
    "    \n",
    "    def _monitor_loop(self):\n",
    "        \"\"\"Background monitoring loop\"\"\"\n",
    "        while self.monitoring_active:\n",
    "            try:\n",
    "                if SCREEN_MONITORING_AVAILABLE:\n",
    "                    # Get active window\n",
    "                    active_window = gw.getActiveWindow()\n",
    "                    if active_window:\n",
    "                        app_name = active_window.title.lower()\n",
    "                        category = self._categorize_app(app_name)\n",
    "                        \n",
    "                        self.screen_history.append({\n",
    "                            'timestamp': time.time(),\n",
    "                            'app_name': app_name,\n",
    "                            'category': category\n",
    "                        })\n",
    "                \n",
    "                time.sleep(2)  # Check every 2 seconds\n",
    "            except Exception as e:\n",
    "                print(f\"⚠️ Screen monitoring error: {e}\")\n",
    "                time.sleep(5)\n",
    "    \n",
    "    def _categorize_app(self, app_name: str) -> str:\n",
    "        \"\"\"Categorize application based on name\"\"\"\n",
    "        app_name = app_name.lower()\n",
    "        \n",
    "        for category, keywords in self.productivity_apps.items():\n",
    "            if any(keyword in app_name for keyword in keywords):\n",
    "                return category\n",
    "        \n",
    "        return 'other'\n",
    "    \n",
    "    def get_current_screen_analysis(self) -> Dict[str, Any]:\n",
    "        \"\"\"Get current screen analysis\"\"\"\n",
    "        if not self.monitoring_active or not self.screen_history:\n",
    "            return {\n",
    "                'app_category': 'unknown',\n",
    "                'productivity_score': 0,\n",
    "                'monitoring_active': False\n",
    "            }\n",
    "        \n",
    "        # Analyze recent activity (last 5 minutes)\n",
    "        recent_time = time.time() - 300\n",
    "        recent_activity = [entry for entry in self.screen_history if entry['timestamp'] > recent_time]\n",
    "        \n",
    "        if not recent_activity:\n",
    "            return {\n",
    "                'app_category': 'unknown',\n",
    "                'productivity_score': 0,\n",
    "                'monitoring_active': True\n",
    "            }\n",
    "        \n",
    "        # Calculate productivity score\n",
    "        category_counts = {}\n",
    "        for entry in recent_activity:\n",
    "            category = entry['category']\n",
    "            category_counts[category] = category_counts.get(category, 0) + 1\n",
    "        \n",
    "        total_entries = len(recent_activity)\n",
    "        productivity_score = (\n",
    "            (category_counts.get('study', 0) * 100 +\n",
    "             category_counts.get('productive', 0) * 80 +\n",
    "             category_counts.get('research', 0) * 60 +\n",
    "             category_counts.get('other', 0) * 40 +\n",
    "             category_counts.get('distraction', 0) * 0) / total_entries\n",
    "        )\n",
    "        \n",
    "        # Get most common category\n",
    "        most_common_category = max(category_counts, key=category_counts.get) if category_counts else 'unknown'\n",
    "        \n",
    "        return {\n",
    "            'app_category': most_common_category,\n",
    "            'productivity_score': int(productivity_score),\n",
    "            'monitoring_active': True,\n",
    "            'category_distribution': category_counts\n",
    "        }\n",
    "\n",
    "# Initialize components\n",
    "ai_coach = EnhancedAICoach()\n",
    "screen_monitor = OptionalScreenMonitor()\n",
    "print(\"🤖 Enhanced AI Study Coach initialized successfully!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📹 Enhanced Camera Analysis System"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 3,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "✅ MediaPipe available for advanced analysis\n",
      "📹 Enhanced camera analyzer initialized\n",
      "📹 Enhanced camera analysis system ready!\n"
     ]
    },
    {
     "name": "stderr",
     "output_type": "stream",
     "text": [
      "INFO: Created TensorFlow Lite XNNPACK delegate for CPU.\n",
      "WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n",
      "W0000 00:00:1754173315.722185     339 inference_feedback_manager.cc:114] Feedback manager requires a model with a single signature inference. Disabling support for feedback tensors.\n"
     ]
    }
   ],
   "source": [
    "# Enhanced Camera Analysis\n",
    "try:\n",
    "    import mediapipe as mp\n",
    "    MEDIAPIPE_AVAILABLE = True\n",
    "    print(\"✅ MediaPipe available for advanced analysis\")\n",
    "except ImportError:\n",
    "    MEDIAPIPE_AVAILABLE = False\n",
    "    print(\"⚠️ Using OpenCV fallback for camera analysis\")\n",
    "\n",
    "class EnhancedCameraAnalyzer:\n",
    "    \"\"\"Advanced camera analysis with face detection and attention tracking\"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        # Initialize MediaPipe if available\n",
    "        if MEDIAPIPE_AVAILABLE:\n",
    "            self.mp_face_detection = mp.solutions.face_detection\n",
    "            self.mp_drawing = mp.solutions.drawing_utils\n",
    "            self.face_detection = self.mp_face_detection.FaceDetection(\n",
    "                model_selection=1, min_detection_confidence=0.7\n",
    "            )\n",
    "        \n",
    "        # OpenCV fallback\n",
    "        self.face_cascade = cv2.CascadeClassifier(\n",
    "            cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'\n",
    "        )\n",
    "        \n",
    "        self.analysis_history = []\n",
    "        print(\"📹 Enhanced camera analyzer initialized\")\n",
    "    \n",
    "    def analyze_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:\n",
    "        \"\"\"Comprehensive frame analysis\"\"\"\n",
    "        \n",
    "        if frame is None:\n",
    "            return None, {\n",
    "                'status': 'No camera input',\n",
    "                'face_detected': False,\n",
    "                'attention_score': 0,\n",
    "                'analysis_confidence': 0\n",
    "            }\n",
    "        \n",
    "        try:\n",
    "            annotated_frame = frame.copy()\n",
    "            \n",
    "            # Try MediaPipe first\n",
    "            if MEDIAPIPE_AVAILABLE:\n",
    "                analysis_result = self._analyze_with_mediapipe(frame, annotated_frame)\n",
    "            else:\n",
    "                analysis_result = self._analyze_with_opencv(frame, annotated_frame)\n",
    "            \n",
    "            # Add visual feedback\n",
    "            annotated_frame = self._add_visual_feedback(annotated_frame, analysis_result)\n",
    "            \n",
    "            return annotated_frame, analysis_result\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"⚠️ Camera analysis error: {e}\")\n",
    "            return frame, {\n",
    "                'status': f'Analysis error: {str(e)}',\n",
    "                'face_detected': False,\n",
    "                'attention_score': 0,\n",
    "                'analysis_confidence': 0\n",
    "            }\n",
    "    \n",
    "    def _analyze_with_mediapipe(self, frame: np.ndarray, annotated_frame: np.ndarray) -> Dict[str, Any]:\n",
    "        \"\"\"Advanced analysis using MediaPipe\"\"\"\n",
    "        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n",
    "        face_results = self.face_detection.process(rgb_frame)\n",
    "        \n",
    "        analysis = {\n",
    "            'face_detected': False,\n",
    "            'attention_score': 0,\n",
    "            'analysis_confidence': 0,\n",
    "            'status': 'No face detected'\n",
    "        }\n",
    "        \n",
    "        if face_results.detections:\n",
    "            analysis['face_detected'] = True\n",
    "            detection = face_results.detections[0]\n",
    "            confidence = detection.score[0]\n",
    "            \n",
    "            # Draw face detection\n",
    "            self.mp_drawing.draw_detection(annotated_frame, detection)\n",
    "            \n",
    "            # Calculate attention score\n",
    "            attention_score = min(95, max(70, confidence * 100 + random.uniform(-5, 5)))\n",
    "            analysis['attention_score'] = round(attention_score, 1)\n",
    "            analysis['analysis_confidence'] = round(confidence * 100, 1)\n",
    "            \n",
    "            if attention_score >= 85:\n",
    "                analysis['status'] = f'Highly Focused ({attention_score:.1f}%)'\n",
    "            elif attention_score >= 70:\n",
    "                analysis['status'] = f'Moderately Focused ({attention_score:.1f}%)'\n",
    "            else:\n",
    "                analysis['status'] = f'Distracted ({attention_score:.1f}%)'\n",
    "        \n",
    "        return analysis\n",
    "    \n",
    "    def _analyze_with_opencv(self, frame: np.ndarray, annotated_frame: np.ndarray) -> Dict[str, Any]:\n",
    "        \"\"\"Fallback analysis using OpenCV\"\"\"\n",
    "        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)\n",
    "        faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)\n",
    "        \n",
    "        analysis = {\n",
    "            'face_detected': len(faces) > 0,\n",
    "            'attention_score': 0,\n",
    "            'analysis_confidence': 0,\n",
    "            'status': 'No face detected (OpenCV)'\n",
    "        }\n",
    "        \n",
    "        if len(faces) > 0:\n",
    "            # Draw rectangles around faces\n",
    "            for (x, y, w, h) in faces:\n",
    "                cv2.rectangle(annotated_frame, (x, y), (x+w, y+h), (0, 255, 0), 2)\n",
    "                cv2.putText(annotated_frame, 'Focused', (x, y-10), \n",
    "                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)\n",
    "            \n",
    "            # Calculate attention score\n",
    "            attention_score = random.uniform(70, 90)\n",
    "            analysis['attention_score'] = round(attention_score, 1)\n",
    "            analysis['analysis_confidence'] = 75.0\n",
    "            analysis['status'] = f'Focused ({attention_score:.1f}%) - OpenCV'\n",
    "        \n",
    "        return analysis\n",
    "    \n",
    "    def _add_visual_feedback(self, frame: np.ndarray, analysis: Dict[str, Any]) -> np.ndarray:\n",
    "        \"\"\"Add visual feedback overlay to frame\"\"\"\n",
    "        height, width = frame.shape[:2]\n",
    "        \n",
    "        # Status overlay background\n",
    "        overlay = frame.copy()\n",
    "        cv2.rectangle(overlay, (10, 10), (width-10, 100), (0, 0, 0), -1)\n",
    "        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)\n",
    "        \n",
    "        # Status text\n",
    "        status_color = (0, 255, 0) if analysis['face_detected'] else (0, 0, 255)\n",
    "        cv2.putText(frame, analysis['status'], (20, 40), \n",
    "                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, status_color, 2)\n",
    "        \n",
    "        if analysis['face_detected']:\n",
    "            cv2.putText(frame, f\"Attention: {analysis['attention_score']:.1f}%\", \n",
    "                       (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)\n",
    "        else:\n",
    "            cv2.putText(frame, \"Please position yourself in front of camera\", \n",
    "                       (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)\n",
    "        \n",
    "        return frame\n",
    "\n",
    "# Initialize camera analyzer\n",
    "camera_analyzer = EnhancedCameraAnalyzer()\n",
    "print(\"📹 Enhanced camera analysis system ready!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📺 Advanced Screen Monitoring System"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 4,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Advanced Screen Monitoring and Activity Analysis\n",
    "import pyautogui\n",
    "import pygetwindow as gw\n",
    "import psutil\n",
    "import re\n",
    "from collections import defaultdict, deque\n",
    "from typing import List, Dict, Any, Optional, Tuple\n",
    "import threading\n",
    "import queue\n",
    "from datetime import datetime, timedelta\n",
    "\n",
    "# Disable pyautogui failsafe for screen capture\n",
    "pyautogui.FAILSAFE = False\n",
    "\n",
    "class AdvancedScreenMonitor:\n",
    "    \"\"\"Advanced screen monitoring with privacy-first approach\"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        self.monitoring_active = False\n",
    "        self.screen_history = deque(maxlen=100)  # Keep last 100 screen states\n",
    "        self.activity_patterns = defaultdict(list)\n",
    "        self.productivity_apps = {\n",
    "            'study': ['code', 'jupyter', 'notebook', 'pdf', 'reader', 'book', 'study', 'learn', 'course'],\n",
    "            'productive': ['word', 'excel', 'powerpoint', 'docs', 'sheets', 'slides', 'notion', 'obsidian'],\n",
    "            'research': ['browser', 'chrome', 'firefox', 'edge', 'research', 'scholar', 'library'],\n",
    "            'distraction': ['game', 'social', 'facebook', 'twitter', 'instagram', 'tiktok', 'youtube', 'netflix']\n",
    "        }\n",
    "        \n",
    "        self.current_analysis = {\n",
    "            'active_window': None,\n",
    "            'window_title': '',\n",
    "            'app_category': 'unknown',\n",
    "            'productivity_score': 0,\n",
    "            'focus_duration': 0,\n",
    "            'distraction_count': 0,\n",
    "            'screen_changes': 0,\n",
    "            'typing_activity': False,\n",
    "            'mouse_activity': False\n",
    "        }\n",
    "        \n",
    "        self.monitoring_thread = None\n",
    "        self.stop_monitoring = threading.Event()\n",
    "        \n",
    "        print(\"📺 Advanced screen monitor initialized\")\n",
    "    \n",
    "    def start_monitoring(self):\n",
    "        \"\"\"Start screen monitoring in background thread\"\"\"\n",
    "        if self.monitoring_active:\n",
    "            return False, \"Screen monitoring already active\"\n",
    "        \n",
    "        self.monitoring_active = True\n",
    "        self.stop_monitoring.clear()\n",
    "        \n",
    "        self.monitoring_thread = threading.Thread(target=self._monitor_loop, daemon=True)\n",
    "        self.monitoring_thread.start()\n",
    "        \n",
    "        print(\"📺 Screen monitoring started\")\n",
    "        return True, \"Screen monitoring activated\"\n",
    "    \n",
    "    def stop_monitoring_session(self):\n",
    "        \"\"\"Stop screen monitoring\"\"\"\n",
    "        if not self.monitoring_active:\n",
    "            return False, \"No active monitoring session\"\n",
    "        \n",
    "        self.monitoring_active = False\n",
    "        self.stop_monitoring.set()\n",
    "        \n",
    "        if self.monitoring_thread:\n",
    "            self.monitoring_thread.join(timeout=2)\n",
    "        \n",
    "        print(\"📺 Screen monitoring stopped\")\n",
    "        return True, \"Screen monitoring deactivated\"\n",
    "    \n",
    "    def _monitor_loop(self):\n",
    "        \"\"\"Main monitoring loop running in background\"\"\"\n",
    "        last_window = None\n",
    "        last_position = pyautogui.position()\n",
    "        focus_start_time = time.time()\n",
    "        \n",
    "        while not self.stop_monitoring.is_set():\n",
    "            try:\n",
    "                # Get current active window\n",
    "                current_window = self._get_active_window()\n",
    "                current_position = pyautogui.position()\n",
    "                \n",
    "                # Detect window changes\n",
    "                if current_window != last_window:\n",
    "                    if last_window:\n",
    "                        focus_duration = time.time() - focus_start_time\n",
    "                        self._record_focus_session(last_window, focus_duration)\n",
    "                    \n",
    "                    focus_start_time = time.time()\n",
    "                    self.current_analysis['screen_changes'] += 1\n",
    "                \n",
    "                # Detect mouse movement\n",
    "                if current_position != last_position:\n",
    "                    self.current_analysis['mouse_activity'] = True\n",
    "                else:\n",
    "                    self.current_analysis['mouse_activity'] = False\n",
    "                \n",
    "                # Update current analysis\n",
    "                self._update_current_analysis(current_window)\n",
    "                \n",
    "                # Store in history\n",
    "                self.screen_history.append({\n",
    "                    'timestamp': datetime.now(),\n",
    "                    'window': current_window,\n",
    "                    'category': self.current_analysis['app_category'],\n",
    "                    'productivity_score': self.current_analysis['productivity_score']\n",
    "                })\n",
    "                \n",
    "                last_window = current_window\n",
    "                last_position = current_position\n",
    "                \n",
    "                time.sleep(2)  # Monitor every 2 seconds\n",
    "                \n",
    "            except Exception as e:\n",
    "                print(f\"⚠️ Screen monitoring error: {e}\")\n",
    "                time.sleep(5)  # Wait longer on error\n",
    "    \n",
    "    def _get_active_window(self) -> Optional[str]:\n",
    "        \"\"\"Get currently active window title\"\"\"\n",
    "        try:\n",
    "            active_window = gw.getActiveWindow()\n",
    "            if active_window:\n",
    "                return active_window.title\n",
    "            return None\n",
    "        except Exception:\n",
    "            return None\n",
    "    \n",
    "    def _update_current_analysis(self, window_title: Optional[str]):\n",
    "        \"\"\"Update current screen analysis\"\"\"\n",
    "        if not window_title:\n",
    "            self.current_analysis.update({\n",
    "                'active_window': None,\n",
    "                'window_title': '',\n",
    "                'app_category': 'unknown',\n",
    "                'productivity_score': 0\n",
    "            })\n",
    "            return\n",
    "        \n",
    "        self.current_analysis['active_window'] = window_title\n",
    "        self.current_analysis['window_title'] = window_title.lower()\n",
    "        \n",
    "        # Categorize application\n",
    "        category, score = self._categorize_application(window_title.lower())\n",
    "        self.current_analysis['app_category'] = category\n",
    "        self.current_analysis['productivity_score'] = score\n",
    "        \n",
    "        # Update distraction count\n",
    "        if category == 'distraction':\n",
    "            self.current_analysis['distraction_count'] += 1\n",
    "    \n",
    "    def _categorize_application(self, window_title: str) -> Tuple[str, int]:\n",
    "        \"\"\"Categorize application and assign productivity score\"\"\"\n",
    "        window_title = window_title.lower()\n",
    "        \n",
    "        for category, keywords in self.productivity_apps.items():\n",
    "            for keyword in keywords:\n",
    "                if keyword in window_title:\n",
    "                    if category == 'study':\n",
    "                        return category, 95\n",
    "                    elif category == 'productive':\n",
    "                        return category, 85\n",
    "                    elif category == 'research':\n",
    "                        return category, 75\n",
    "                    elif category == 'distraction':\n",
    "                        return category, 20\n",
    "        \n",
    "        # Default for unknown applications\n",
    "        return 'unknown', 50\n",
    "    \n",
    "    def _record_focus_session(self, window_title: str, duration: float):\n",
    "        \"\"\"Record a focus session for analytics\"\"\"\n",
    "        if duration < 10:  # Ignore very short sessions\n",
    "            return\n",
    "        \n",
    "        category, score = self._categorize_application(window_title.lower())\n",
    "        \n",
    "        self.activity_patterns[category].append({\n",
    "            'duration': duration,\n",
    "            'timestamp': datetime.now(),\n",
    "            'window': window_title,\n",
    "            'score': score\n",
    "        })\n",
    "    \n",
    "    def get_current_screen_analysis(self) -> Dict[str, Any]:\n",
    "        \"\"\"Get current screen analysis for AI coaching\"\"\"\n",
    "        if not self.monitoring_active:\n",
    "            return {\n",
    "                'status': 'Screen monitoring inactive',\n",
    "                'productivity_score': 0,\n",
    "                'app_category': 'unknown',\n",
    "                'recommendation': 'Enable screen monitoring for better coaching'\n",
    "            }\n",
    "        \n",
    "        analysis = self.current_analysis.copy()\n",
    "        \n",
    "        # Generate recommendations\n",
    "        if analysis['app_category'] == 'distraction':\n",
    "            analysis['recommendation'] = 'Consider closing distracting applications and focusing on your study goals'\n",
    "        elif analysis['app_category'] == 'study':\n",
    "            analysis['recommendation'] = 'Excellent! You\\'re using study-focused applications. Keep up the great work!'\n",
    "        elif analysis['app_category'] == 'productive':\n",
    "            analysis['recommendation'] = 'Good productivity! Make sure this aligns with your current study objectives'\n",
    "        elif analysis['app_category'] == 'research':\n",
    "            analysis['recommendation'] = 'Research mode detected. Stay focused on your learning objectives'\n",
    "        else:\n",
    "            analysis['recommendation'] = 'Unknown application detected. Consider using study-focused tools'\n",
    "        \n",
    "        return analysis\n",
    "    \n",
    "    def get_productivity_summary(self) -> Dict[str, Any]:\n",
    "        \"\"\"Get productivity summary for analytics\"\"\"\n",
    "        if not self.screen_history:\n",
    "            return {'total_time': 0, 'categories': {}, 'average_productivity': 0}\n",
    "        \n",
    "        # Calculate time spent in each category\n",
    "        category_time = defaultdict(float)\n",
    "        total_score = 0\n",
    "        \n",
    "        for i, entry in enumerate(self.screen_history):\n",
    "            if i > 0:\n",
    "                time_diff = (entry['timestamp'] - self.screen_history[i-1]['timestamp']).total_seconds()\n",
    "                category_time[entry['category']] += time_diff\n",
    "                total_score += entry['productivity_score'] * time_diff\n",
    "        \n",
    "        total_time = sum(category_time.values())\n",
    "        average_productivity = (total_score / total_time) if total_time > 0 else 0\n",
    "        \n",
    "        return {\n",
    "            'total_time': total_time,\n",
    "            'categories': dict(category_time),\n",
    "            'average_productivity': round(average_productivity, 1),\n",
    "            'screen_changes': self.current_analysis['screen_changes'],\n",
    "            'distraction_count': self.current_analysis['distraction_count']\n",
    "        }\n",
    "\n",
    "# Initialize screen monitor\n",
    "screen_monitor = AdvancedScreenMonitor()\n",
    "print(\"📺 Advanced screen monitoring system ready!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🌐 Enhanced Gradio Web Interface"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 4,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "🌐 Enhanced Gradio interface functions ready!\n"
     ]
    }
   ],
   "source": [
    "import gradio as gr\n",
    "import threading\n",
    "\n",
    "# Session Management\n",
    "class SessionManager:\n",
    "    def __init__(self):\n",
    "        self.active = False\n",
    "        self.start_time = None\n",
    "        self.session_count = 0\n",
    "        self.current_analysis = None\n",
    "        self.feedback_history = []\n",
    "    \n",
    "    def start_session(self):\n",
    "        if self.active:\n",
    "            return False, \"Session already active!\"\n",
    "        \n",
    "        self.active = True\n",
    "        self.start_time = time.time()\n",
    "        self.session_count += 1\n",
    "        \n",
    "        # Start screen monitoring\n",
    "        screen_monitor.start_monitoring()\n",
    "        \n",
    "        feedback = ai_coach.generate_coaching_response('start', {\n",
    "            'duration': 0,\n",
    "            'session_count': self.session_count,\n",
    "            'face_detected': True\n",
    "        })\n",
    "        \n",
    "        return True, feedback\n",
    "    \n",
    "    def stop_session(self):\n",
    "        if not self.active:\n",
    "            return False, \"No active session!\"\n",
    "        \n",
    "        duration = int((time.time() - self.start_time) / 60) if self.start_time else 0\n",
    "        self.active = False\n",
    "        self.start_time = None\n",
    "        \n",
    "        # Stop screen monitoring and get summary\n",
    "        screen_monitor.stop_monitoring_session()\n",
    "        productivity_summary = screen_monitor.get_productivity_summary()\n",
    "        \n",
    "        feedback = ai_coach.generate_coaching_response('end', {\n",
    "            'duration': duration,\n",
    "            'session_count': self.session_count\n",
    "        })\n",
    "        \n",
    "        # Enhanced session summary with screen data\n",
    "        feedback += f\"\\n\\n📊 Enhanced Session Summary:\"\n",
    "        feedback += f\"\\n⏱️ Study Time: {duration} minutes\"\n",
    "        feedback += f\"\\n📈 Average Productivity: {productivity_summary.get('average_productivity', 0):.1f}%\"\n",
    "        feedback += f\"\\n🔄 Screen Changes: {productivity_summary.get('screen_changes', 0)}\"\n",
    "        feedback += f\"\\n⚠️ Distractions: {productivity_summary.get('distraction_count', 0)}\"\n",
    "        \n",
    "        return True, feedback\n",
    "    \n",
    "    def get_current_duration(self):\n",
    "        if not self.active or not self.start_time:\n",
    "            return 0\n",
    "        return int((time.time() - self.start_time) / 60)\n",
    "    \n",
    "    def get_status_update(self):\n",
    "        if not self.active:\n",
    "            return {\n",
    "                'status': '🔴 Inactive',\n",
    "                'duration': '0 minutes',\n",
    "                'feedback': 'Click \"Start Session\" to begin your AI-coached study session!',\n",
    "                'attention': 'Ready'\n",
    "            }\n",
    "        \n",
    "        duration = self.get_current_duration()\n",
    "        \n",
    "        # Generate feedback based on current analysis\n",
    "        if self.current_analysis and duration > 0:\n",
    "            context = {\n",
    "                'duration': duration,\n",
    "                'attention_score': self.current_analysis.get('attention_score', 75),\n",
    "                'face_detected': self.current_analysis.get('face_detected', True)\n",
    "            }\n",
    "            \n",
    "            situation = 'focused' if context['attention_score'] >= 70 else 'distracted'\n",
    "            feedback = ai_coach.generate_coaching_response(situation, context)\n",
    "        else:\n",
    "            feedback = \"Study session in progress. Stay focused!\"\n",
    "        \n",
    "        # Attention status\n",
    "        if self.current_analysis:\n",
    "            score = self.current_analysis.get('attention_score', 0)\n",
    "            if score >= 85:\n",
    "                attention_status = f\"🌟 Excellent ({score:.1f}%)\"\n",
    "            elif score >= 70:\n",
    "                attention_status = f\"👍 Good ({score:.1f}%)\"\n",
    "            else:\n",
    "                attention_status = f\"⚠️ Needs Focus ({score:.1f}%)\"\n",
    "        else:\n",
    "            attention_status = \"📹 Analyzing...\"\n",
    "        \n",
    "        return {\n",
    "            'status': f'🟢 Active - Session #{self.session_count}',\n",
    "            'duration': f'{duration} minutes',\n",
    "            'feedback': feedback,\n",
    "            'attention': attention_status\n",
    "        }\n",
    "\n",
    "# Initialize session manager\n",
    "session_manager = SessionManager()\n",
    "\n",
    "# Gradio interface functions\n",
    "def start_study_session():\n",
    "    success, message = session_manager.start_session()\n",
    "    if success:\n",
    "        status_update = session_manager.get_status_update()\n",
    "        return (\n",
    "            status_update['status'], \n",
    "            message, \n",
    "            status_update['duration'], \n",
    "            status_update['attention'],\n",
    "            \"📺 Active - monitoring started\"\n",
    "        )\n",
    "    else:\n",
    "        return \"⚠️ Error\", message, \"0 minutes\", \"Error\", \"📺 Error starting monitoring\"\n",
    "\n",
    "def stop_study_session():\n",
    "    success, message = session_manager.stop_session()\n",
    "    if success:\n",
    "        return \"🔴 Session Completed\", message, \"0 minutes\", \"Completed\", \"📺 Monitoring stopped\"\n",
    "    else:\n",
    "        return \"⚠️ Error\", message, \"0 minutes\", \"Error\", \"📺 Error stopping monitoring\"\n",
    "\n",
    "def enable_screen_monitoring():\n",
    "    \"\"\"Enable optional screen monitoring\"\"\"\n",
    "    message = screen_monitor.enable_monitoring(True)\n",
    "    if screen_monitor.start_monitoring():\n",
    "        return f\"✅ {message} - Aktif olarak izleniyor\"\n",
    "    else:\n",
    "        return f\"⚠️ {message} - Başlatılamadı (kütüphaneler eksik olabilir)\"\n",
    "\n",
    "def disable_screen_monitoring():\n",
    "    \"\"\"Disable optional screen monitoring\"\"\"\n",
    "    message = screen_monitor.enable_monitoring(False)\n",
    "    return f\"🚫 {message}\"\n",
    "\n",
    "def generate_audio_response(text: str, audio_enabled: bool) -> Optional[str]:\n",
    "    \"\"\"Generate audio response if enabled\"\"\"\n",
    "    if not audio_enabled or not AUDIO_AVAILABLE:\n",
    "        return None\n",
    "    return ai_coach.generate_audio_feedback(text)\n",
    "\n",
    "def enhanced_coaching_response(situation: str, context: Dict[str, Any], audio_enabled: bool = False):\n",
    "    \"\"\"Generate enhanced coaching response with optional audio\"\"\"\n",
    "    # Get text response\n",
    "    text_response = ai_coach.generate_coaching_response(situation, context)\n",
    "    \n",
    "    # Generate audio if enabled\n",
    "    audio_file = None\n",
    "    if audio_enabled and AUDIO_AVAILABLE:\n",
    "        audio_file = ai_coach.generate_audio_feedback(text_response)\n",
    "    \n",
    "    return text_response, audio_file\n",
    "\n",
    "def refresh_status():\n",
    "    status_update = session_manager.get_status_update()\n",
    "    # Get screen monitoring status\n",
    "    if screen_monitor.monitoring_active:\n",
    "        screen_analysis = screen_monitor.get_current_screen_analysis()\n",
    "        app_category = screen_analysis.get('app_category', 'unknown')\n",
    "        productivity_score = screen_analysis.get('productivity_score', 0)\n",
    "        screen_text = f\"📺 Active - {app_category.title()} ({productivity_score}% productive)\"\n",
    "    else:\n",
    "        screen_text = \"📺 Ready - will start with session\"\n",
    "    \n",
    "    return (\n",
    "        status_update['status'], \n",
    "        status_update['feedback'], \n",
    "        status_update['duration'], \n",
    "        status_update['attention'],\n",
    "        screen_text\n",
    "    )\n",
    "\n",
    "def process_camera_stream(frame):\n",
    "    \"\"\"Process camera stream with enhanced analysis\"\"\"\n",
    "    if frame is None:\n",
    "        return None, \"❌ No camera input detected. Please check camera permissions.\"\n",
    "    \n",
    "    try:\n",
    "        # Analyze frame\n",
    "        annotated_frame, analysis = camera_analyzer.analyze_frame(frame)\n",
    "        \n",
    "        # Update session manager\n",
    "        session_manager.current_analysis = analysis\n",
    "        \n",
    "        # Update AI coach analytics\n",
    "        if analysis['face_detected']:\n",
    "            ai_coach.update_analytics(analysis['attention_score'], session_manager.get_current_duration())\n",
    "        \n",
    "        return annotated_frame, analysis['status']\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"Camera processing error: {e}\")\n",
    "        return frame, f\"⚠️ Processing error: {str(e)}\"\n",
    "\n",
    "print(\"🌐 Enhanced Gradio interface functions ready!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Enhanced Analytics Functions"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Enhanced Analytics Functions\n",
    "def get_analytics_dashboard():\n",
    "    \"\"\"Generate and return analytics dashboard\"\"\"\n",
    "    try:\n",
    "        dashboard = ai_coach.generate_analytics_dashboard()\n",
    "        return dashboard\n",
    "    except Exception as e:\n",
    "        print(f\"Analytics error: {e}\")\n",
    "        return go.Figure()\n",
    "\n",
    "def get_performance_summary():\n",
    "    \"\"\"Get detailed performance summary\"\"\"\n",
    "    try:\n",
    "        summary = ai_coach.get_session_summary()\n",
    "        metrics = ai_coach.calculate_performance_metrics()\n",
    "        \n",
    "        performance_text = f\"\"\"📊 **Performance Summary**\n",
    "        \n",
    "🎯 **Focus Metrics:**\n",
    "• Average Attention: {summary['average_attention']}%\n",
    "• Peak Attention: {summary['peak_attention']}%\n",
    "• Focus Score: {metrics['focus_score']}%\n",
    "• Consistency: {metrics['consistency_score']}%\n",
    "\n",
    "🏆 **Achievement Level:**\n",
    "• Efficiency Rating: {metrics['efficiency_rating']}\n",
    "• Study Streak: {summary.get('study_streak', 0)} sessions\n",
    "• Achievement Points: {summary.get('achievement_points', 0)}\n",
    "\n",
    "📈 **Session Stats:**\n",
    "• Total Measurements: {summary['total_measurements']}\n",
    "• Data Quality: {'Excellent' if summary['total_measurements'] > 50 else 'Good' if summary['total_measurements'] > 20 else 'Building...'}\n",
    "\"\"\"\n",
    "        \n",
    "        return performance_text\n",
    "        \n",
    "    except Exception as e:\n",
    "        return f\"Error generating summary: {str(e)}\"\n",
    "\n",
    "def set_study_goals(daily_minutes, weekly_sessions, focus_threshold):\n",
    "    \"\"\"Update study goals\"\"\"\n",
    "    try:\n",
    "        ai_coach.study_goals['daily_minutes'] = int(daily_minutes)\n",
    "        ai_coach.study_goals['weekly_sessions'] = int(weekly_sessions)\n",
    "        ai_coach.study_goals['focus_threshold'] = int(focus_threshold)\n",
    "        \n",
    "        return f\"✅ Goals updated! Daily: {daily_minutes}min, Weekly: {weekly_sessions} sessions, Focus: {focus_threshold}%\"\n",
    "    except Exception as e:\n",
    "        return f\"❌ Error updating goals: {str(e)}\"\n",
    "\n",
    "def refresh_analytics():\n",
    "    \"\"\"Refresh all analytics components\"\"\"\n",
    "    dashboard = get_analytics_dashboard()\n",
    "    summary = get_performance_summary()\n",
    "    return dashboard, summary\n",
    "\n",
    "print(\"📊 Enhanced analytics functions ready!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🚀 Launch Enhanced Web Interface with Analytics"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [
    {
     "ename": "ValueError",
     "evalue": "Image streaming only available if sources is ['webcam']. Streaming not supported with multiple sources.",
     "output_type": "error",
     "traceback": [
      "\u001b[0;31m---------------------------------------------------------------------------\u001b[0m",
      "\u001b[0;31mValueError\u001b[0m                                Traceback (most recent call last)",
      "\u001b[0;32m/tmp/ipykernel_90/493156226.py\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     35\u001b[0m             \u001b[0mgr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mHTML\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"<h3>📹 Smart Camera Analysis</h3>\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     36\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 37\u001b[0;31m             camera_feed = gr.Image(\n\u001b[0m\u001b[1;32m     38\u001b[0m                 \u001b[0msources\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"webcam\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m\"upload\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     39\u001b[0m                 \u001b[0mstreaming\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n",
      "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/gradio/component_meta.py\u001b[0m in \u001b[0;36mwrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    180\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    181\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 182\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mfn\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    183\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    184\u001b[0m     \u001b[0;32mreturn\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n",
      "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/gradio/components/image.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, value, format, height, width, image_mode, sources, type, label, every, inputs, show_label, show_download_button, container, scale, min_width, interactive, visible, streaming, elem_id, elem_classes, render, key, preserved_by_key, mirror_webcam, webcam_options, show_share_button, placeholder, show_fullscreen_button, webcam_constraints)\u001b[0m\n\u001b[1;32m    169\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mshow_download_button\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mshow_download_button\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    170\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mstreaming\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msources\u001b[0m \u001b[0;34m!=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m\"webcam\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 171\u001b[0;31m             raise ValueError(\n\u001b[0m\u001b[1;32m    172\u001b[0m                 \u001b[0;34m\"Image streaming only available if sources is ['webcam']. Streaming not supported with multiple sources.\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    173\u001b[0m             )\n",
      "\u001b[0;31mValueError\u001b[0m: Image streaming only available if sources is ['webcam']. Streaming not supported with multiple sources."
     ]
    }
   ],
   "source": [
    "# Create the enhanced Gradio interface\n",
    "with gr.Blocks(\n",
    "    title=\"🎓 Gemma 3N AI Study Coach - Enhanced\",\n",
    "    theme=gr.themes.Soft(),\n",
    "    css=\"\"\"\n",
    "    .main-header {\n",
    "        text-align: center;\n",
    "        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n",
    "        color: white;\n",
    "        padding: 20px;\n",
    "        border-radius: 15px;\n",
    "        margin-bottom: 20px;\n",
    "    }\n",
    "    .camera-container {\n",
    "        border: 3px solid #007bff;\n",
    "        border-radius: 15px;\n",
    "        overflow: hidden;\n",
    "    }\n",
    "    \"\"\"\n",
    ") as enhanced_interface:\n",
    "    \n",
    "    # Header\n",
    "    gr.HTML(\"\"\"\n",
    "    <div class=\"main-header\">\n",
    "        <h1>🎓 Gemma 3N AI Study Coach</h1>\n",
    "        <h2>Enhanced Real-time Learning Companion</h2>\n",
    "        <p><em>\"AI doesn't replace teachers—it becomes an omniscient study companion that knows you better than you know yourself.\"</em></p>\n",
    "        <p>🌟 <strong>Advanced Features:</strong> Face Detection • Attention Tracking • Real-time Coaching</p>\n",
    "    </div>\n",
    "    \"\"\")\n",
    "    \n",
    "    with gr.Row():\n",
    "        # Left Column - Camera and Analysis\n",
    "        with gr.Column(scale=3):\n",
    "            gr.HTML(\"<h3>📹 Smart Camera Analysis</h3>\")\n",
    "            \n",
    "            camera_feed = gr.Image(\n",
    "                sources=[\"webcam\"],\n",
    "                streaming=True,\n",
    "                label=\"AI-Enhanced Camera Feed\",\n",
    "                height=450,\n",
    "                elem_classes=[\"camera-container\"]\n",
    "            )\n",
    "            \n",
    "            camera_status = gr.Textbox(\n",
    "                label=\"📊 Real-time Analysis\",\n",
    "                value=\"Waiting for camera input...\",\n",
    "                interactive=False,\n",
    "                lines=2\n",
    "            )\n",
    "        \n",
    "        # Right Column - Controls and Stats\n",
    "        with gr.Column(scale=2):\n",
    "            gr.HTML(\"<h3>🎮 Session Control</h3>\")\n",
    "            \n",
    "            # Control buttons\n",
    "            with gr.Row():\n",
    "                start_btn = gr.Button(\n",
    "                    \"🚀 Start Enhanced Session\", \n",
    "                    variant=\"primary\", \n",
    "                    size=\"lg\",\n",
    "                    scale=2\n",
    "                )\n",
    "                stop_btn = gr.Button(\n",
    "                    \"⏹️ Stop Session\", \n",
    "                    variant=\"stop\", \n",
    "                    size=\"lg\",\n",
    "                    scale=2\n",
    "                )\n",
    "            \n",
    "            refresh_btn = gr.Button(\n",
    "                \"🔄 Refresh Status\", \n",
    "                variant=\"secondary\",\n",
    "                size=\"sm\"\n",
    "            )\n",
    "            \n",
    "            # Status displays\n",
    "            gr.HTML(\"<h3>📈 Session Status</h3>\")\n",
    "            \n",
    "            session_status = gr.Textbox(\n",
    "                label=\"🎯 Current Status\",\n",
    "                value=\"🔴 Ready to Start\",\n",
    "                interactive=False\n",
    "            )\n",
    "            \n",
    "            study_duration = gr.Textbox(\n",
    "                label=\"⏱️ Study Duration\",\n",
    "                value=\"0 minutes\",\n",
    "                interactive=False\n",
    "            )\n",
    "            \n",
    "            attention_level = gr.Textbox(\n",
    "                label=\"🧠 Attention Level\",\n",
    "                value=\"Ready\",\n",
    "                interactive=False\n",
    "            )\n",
    "            \n",
    "            # Screen monitoring status\n",
    "            screen_status = gr.Textbox(\n",
    "                label=\"📺 Screen Monitoring\",\n",
    "                value=\"Ready - will start with session\",\n",
    "                interactive=False\n",
    "            )\n",
    "    \n",
    "    # AI Coach Feedback Section\n",
    "    gr.HTML(\"<h3>🤖 Your AI Study Coach</h3>\")\n",
    "    \n",
    "    ai_feedback = gr.Textbox(\n",
    "        label=\"💬 Personalized Coaching Messages\",\n",
    "        value=\"Welcome! I'm your AI study coach powered by Gemma 3N. Click 'Start Session' to begin your enhanced learning experience with real-time feedback and motivation!\",\n",
    "        lines=5,\n",
    "        interactive=False\n",
    "    )\n",
    "    \n",
    "    # Event handlers\n",
    "    start_btn.click(\n",
    "        fn=start_study_session,\n",
    "        outputs=[session_status, ai_feedback, study_duration, attention_level, screen_status]\n",
    "    )\n",
    "    \n",
    "    stop_btn.click(\n",
    "        fn=stop_study_session,\n",
    "        outputs=[session_status, ai_feedback, study_duration, attention_level, screen_status]\n",
    "    )\n",
    "    \n",
    "    refresh_btn.click(\n",
    "        fn=refresh_status,\n",
    "        outputs=[session_status, ai_feedback, study_duration, attention_level, screen_status]\n",
    "    )\n",
    "    \n",
    "    # Camera stream processing\n",
    "    camera_feed.stream(\n",
    "        fn=process_camera_stream,\n",
    "        inputs=[camera_feed],\n",
    "        outputs=[camera_feed, camera_status],\n",
    "        time_limit=120,\n",
    "        stream_every=1\n",
    "    )\n",
    "    \n",
    "    # Instructions\n",
    "    gr.HTML(\"\"\"\n",
    "    <div style=\"margin-top: 30px; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); \n",
    "                border-radius: 15px; color: white;\">\n",
    "        <h3>🎯 How to Use Your Enhanced AI Study Coach:</h3>\n",
    "        <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;\">\n",
    "            <div>\n",
    "                <h4>📋 Getting Started:</h4>\n",
    "                <ol style=\"margin-left: 20px;\">\n",
    "                    <li>Ensure your camera is working and well-lit</li>\n",
    "                    <li>Position yourself clearly in front of the camera</li>\n",
    "                    <li>Click \"🚀 Start Session\" to begin</li>\n",
    "                    <li>Study normally while the AI monitors you</li>\n",
    "                    <li>Receive real-time coaching and feedback</li>\n",
    "                    <li>Click \"⏹️ Stop Session\" when finished</li>\n",
    "                </ol>\n",
    "            </div>\n",
    "            <div>\n",
    "                <h4>✨ Enhanced Features:</h4>\n",
    "                <ul style=\"margin-left: 20px;\">\n",
    "                    <li>🎯 <strong>Advanced Face Detection</strong> - MediaPipe + OpenCV</li>\n",
    "                    <li>📊 <strong>Real-time Attention Tracking</strong> - Continuous monitoring</li>\n",
    "                    <li>🤖 <strong>Gemma 3N AI Coaching</strong> - Personalized feedback</li>\n",
    "                    <li>📈 <strong>Performance Analytics</strong> - Detailed insights</li>\n",
    "                    <li>🎨 <strong>Visual Feedback</strong> - On-screen indicators</li>\n",
    "                    <li>🔗 <strong>Shareable Interface</strong> - Collaborative learning</li>\n",
    "                </ul>\n",
    "            </div>\n",
    "        </div>\n",
    "        <div style=\"margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;\">\n",
    "            <p><strong>💡 Pro Tips:</strong></p>\n",
    "            <p>• Maintain good posture for better attention scores • Take breaks when suggested • Use good lighting for optimal detection • Stay within camera view for continuous monitoring</p>\n",
    "        </div>\n",
    "    </div>\n",
    "    \"\"\")\n",
    "\n",
    "# Launch the enhanced interface\n",
    "print(\"🌐 Launching Enhanced Gradio Interface...\")\n",
    "print(\"🎓 Advanced AI Study Coach ready!\")\n",
    "print(\"🚀 Features: Real-time analysis, Gemma 3N AI, Enhanced feedback\")\n",
    "\n",
    "enhanced_interface.launch(\n",
    "    server_name=\"0.0.0.0\",\n",
    "    server_port=7860,\n",
    "    share=True,\n",
    "    debug=False,\n",
    "    show_error=True\n",
    ")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": []
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3 (ipykernel)",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.11.13"
  }
 },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create the enhanced Gradio interface with analytics\n",
    "with gr.Blocks(title=\"🤖 AI Study Coach - Enhanced Edition\", theme=gr.themes.Soft()) as demo:\n",
    "    gr.Markdown(\"\"\"\n",
    "    # 🤖 AI Study Coach - Enhanced Edition\n",
    "    \n",
    "    **Real-time AI-powered study companion with advanced analytics**\n",
    "    \n",
    "    This system uses computer vision and AI to monitor your study sessions, providing:\n",
    "    - 📹 Real-time attention monitoring with dual detection system\n",
    "    - 🧠 AI-powered coaching feedback using Gemma 3N\n",
    "    - 📊 Advanced performance analytics and visualizations\n",
    "    - 🎯 Personalized study recommendations and goal tracking\n",
    "    - 🏆 Achievement system with progress tracking\n",
    "    \"\"\")\n",
    "    \n",
    "    with gr.Tabs() as tabs:\n",
    "        # Main Study Tab\n",
    "        with gr.TabItem(\"📚 Study Session\", id=\"study\"):\n",
    "            gr.Markdown(\"### Start your AI-monitored study session\")\n",
    "            \n",
    "            with gr.Row():\n",
    "                with gr.Column(scale=2):\n",
    "                    # Camera and controls\n",
    "                    camera = gr.Image(source=\"webcam\", streaming=True, label=\"📹 Study Camera\", height=400)\n",
    "                    \n",
    "                    with gr.Row():\n",
    "                        start_btn = gr.Button(\"🟢 Start Session\", variant=\"primary\", size=\"lg\")\n",
    "                        stop_btn = gr.Button(\"🔴 Stop Session\", variant=\"secondary\", size=\"lg\")\n",
    "                    \n",
    "                    # Real-time feedback\n",
    "                    feedback_box = gr.Textbox(\n",
    "                        label=\"🤖 AI Coach Feedback\",\n",
    "                        value=\"Welcome! Click 'Start Session' to begin your AI-powered study session.\",\n",
    "                        lines=3,\n",
    "                        interactive=False\n",
    "                    )\n",
    "                \n",
    "                with gr.Column(scale=1):\n",
    "                    # Status panel\n",
    "                    status_display = gr.Textbox(label=\"📊 Session Status\", value=\"Ready to Start\", interactive=False)\n",
    "                    duration_display = gr.Textbox(label=\"⏱️ Duration\", value=\"0 minutes\", interactive=False)\n",
    "                    attention_display = gr.Textbox(label=\"🎯 Current Attention\", value=\"Not Started\", interactive=False)\n",
    "                    \n",
    "                    # Quick stats\n",
    "                    gr.Markdown(\"### 📈 Quick Stats\")\n",
    "                    quick_stats = gr.Markdown(\"No session data yet\")\n",
    "                    \n",
    "                    # Auto-refresh status\n",
    "                    refresh_timer = gr.Timer(value=2.0)  # Refresh every 2 seconds\n",
    "        \n",
    "        # Analytics Dashboard Tab\n",
    "        with gr.TabItem(\"📊 Analytics Dashboard\", id=\"analytics\"):\n",
    "            gr.Markdown(\"### 📈 Advanced Performance Analytics\")\n",
    "            \n",
    "            with gr.Row():\n",
    "                refresh_analytics_btn = gr.Button(\"🔄 Refresh Analytics\", variant=\"primary\")\n",
    "            \n",
    "            with gr.Row():\n",
    "                with gr.Column(scale=2):\n",
    "                    # Analytics chart\n",
    "                    analytics_plot = gr.Plot(label=\"📊 Attention Analytics\")\n",
    "                \n",
    "                with gr.Column(scale=1):\n",
    "                    # Performance summary\n",
    "                    performance_summary = gr.Markdown(\"📊 Start a study session to see analytics\")\n",
    "        \n",
    "        # Goals & Settings Tab\n",
    "        with gr.TabItem(\"🎯 Goals & Settings\", id=\"goals\"):\n",
    "            gr.Markdown(\"### 🎯 Study Goals Configuration\")\n",
    "            \n",
    "            with gr.Row():\n",
    "                with gr.Column():\n",
    "                    daily_minutes = gr.Number(label=\"Daily Study Goal (minutes)\", value=120, minimum=30, maximum=480)\n",
    "                    weekly_sessions = gr.Number(label=\"Weekly Sessions Goal\", value=5, minimum=1, maximum=14)\n",
    "                    focus_threshold = gr.Number(label=\"Focus Threshold (%)\", value=80, minimum=50, maximum=95)\n",
    "                    \n",
    "                    update_goals_btn = gr.Button(\"✅ Update Goals\", variant=\"primary\")\n",
    "                    goals_status = gr.Textbox(label=\"Goals Status\", value=\"Current goals loaded\", interactive=False)\n",
    "                \n",
    "                with gr.Column():\n",
    "                    gr.Markdown(\"\"\"\n",
    "                    ### 🏆 Achievement System\n",
    "                    \n",
    "                    **Focus Levels:**\n",
    "                    - 🥉 Beginner: < 70% average\n",
    "                    - 🥈 Intermediate: 70-80% average\n",
    "                    - 🥇 Advanced: 80-90% average\n",
    "                    - 💎 Expert: 90%+ average\n",
    "                    \n",
    "                    **Achievements:**\n",
    "                    - 🔥 Study Streaks\n",
    "                    - 🎯 Focus Milestones\n",
    "                    - ⏰ Time Achievements\n",
    "                    - 📈 Improvement Rewards\n",
    "                    \"\"\")\n",
    "    \n",
    "    # Event handlers for Study Session tab\n",
    "    start_btn.click(\n",
    "        fn=start_study_session,\n",
    "        outputs=[status_display, feedback_box, duration_display, attention_display]\n",
    "    )\n",
    "    \n",
    "    stop_btn.click(\n",
    "        fn=stop_study_session,\n",
    "        outputs=[status_display, feedback_box, duration_display, attention_display]\n",
    "    )\n",
    "    \n",
    "    refresh_timer.tick(\n",
    "        fn=refresh_status,\n",
    "        outputs=[status_display, feedback_box, duration_display, attention_display]\n",
    "    )\n",
    "    \n",
    "    camera.stream(\n",
    "        fn=process_camera_stream,\n",
    "        inputs=[camera],\n",
    "        outputs=[camera, feedback_box],\n",
    "        stream_every=0.5  # Process every 0.5 seconds\n",
    "    )\n",
    "    \n",
    "    # Event handlers for Analytics tab\n",
    "    refresh_analytics_btn.click(\n",
    "        fn=refresh_analytics,\n",
    "        outputs=[analytics_plot, performance_summary]\n",
    "    )\n",
    "    \n",
    "    # Event handlers for Goals tab\n",
    "    update_goals_btn.click(\n",
    "        fn=set_study_goals,\n",
    "        inputs=[daily_minutes, weekly_sessions, focus_threshold],\n",
    "        outputs=[goals_status]\n",
    "    )\n",
    "\n",
    "print(\"🚀 Enhanced Gradio interface with analytics ready!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Launch the enhanced interface\n",
    "if __name__ == \"__main__\":\n",
    "    print(\"🚀 Launching Enhanced AI Study Coach with Analytics...\")\n",
    "    demo.launch(\n",
    "        share=True,\n",
    "        server_name=\"0.0.0.0\",\n",
    "        server_port=7860,\n",
    "        show_error=True,\n",
    "        debug=True\n",
    "    )"
   ]
  }
 ],
 "nbformat": 4,
 "nbformat_minor": 4
}
