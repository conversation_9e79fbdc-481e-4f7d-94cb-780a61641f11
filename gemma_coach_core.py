#!/usr/bin/env python3
"""
Gemma 3N AI Coach - Core Module
Ana AI koçluk sistemi ve Gemma 3N entegrasyonu
"""

import torch
import gc
import time
import io
import numpy as np
from typing import Dict, Any, Optional, List
from collections import deque

# Unsloth ve model imports
try:
    from unsloth import FastLanguageModel
    UNSLOTH_AVAILABLE = True
except ImportError:
    UNSLOTH_AVAILABLE = False
    print("⚠️ Unsloth not available - install with: pip install unsloth")

# Audio processing imports
try:
    import pyttsx3
    import speech_recognition as sr
    import sounddevice as sd
    import soundfile as sf
    import librosa
    AUDIO_AVAILABLE = True
except ImportError:
    AUDIO_AVAILABLE = False
    print("⚠️ Audio features disabled - install: pip install pyttsx3 speechrecognition sounddevice soundfile librosa")

# Computer vision imports
try:
    import cv2
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    print("⚠️ MediaPipe disabled - install: pip install opencv-python mediapipe")


class GemmaCoachCore:
    """Gemma 3N AI Coach - Ana sınıf"""
    
    def __init__(self, model_name: str = "unsloth/Gemma-2-9B-It-GGUF"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model_loaded = False
        
        # Session tracking
        self.session_history = deque(maxlen=100)
        self.current_session = None
        
        print(f"🤖 Gemma Coach Core initialized on {self.device}")
    
    def load_model(self, max_seq_length: int = 2048, load_in_4bit: bool = True):
        """Gemma 3N modelini yükle"""
        if not UNSLOTH_AVAILABLE:
            print("❌ Unsloth not available - cannot load model")
            return False
        
        try:
            print(f"📥 Loading {self.model_name}...")
            
            # Load model with Unsloth
            self.model, self.tokenizer = FastLanguageModel.from_pretrained(
                model_name=self.model_name,
                max_seq_length=max_seq_length,
                dtype=None,
                load_in_4bit=load_in_4bit,
            )
            
            # Enable inference mode
            FastLanguageModel.for_inference(self.model)
            
            self.model_loaded = True
            print("✅ Gemma 3N model loaded successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Model loading failed: {e}")
            self.model_loaded = False
            return False
    
    def _build_multimodal_content(self, situation: str, context: Dict[str, Any], 
                                  image_data: Optional[str] = None, 
                                  audio_data: Optional[str] = None) -> List[Dict[str, Any]]:
        """Gemma 3N için multimodal içerik oluştur"""
        content = []
        
        # Add image if available
        if image_data:
            content.append({
                "type": "image",
                "image": image_data
            })
        
        # Add audio if available
        if audio_data:
            content.append({
                "type": "audio",
                "audio": audio_data
            })
        
        # Add text context
        duration = context.get('duration', 0)
        attention_score = context.get('attention_score', 75)
        face_detected = context.get('face_detected', True)
        screen_active = context.get('screen_monitoring_active', False)
        app_category = context.get('current_app_category', 'bilinmiyor')
        productivity_score = context.get('productivity_score', 0)
        
        text_content = f"""ÖĞRENCİ DURUMU:
- Çalışma süresi: {duration} dakika
- Dikkat seviyesi: %{attention_score}
- Yüz tespit edildi: {'Evet' if face_detected else 'Hayır'}
- Mevcut durum: {situation}

EKRAN AKTİVİTESİ:
- Ekran izleme: {'Aktif' if screen_active else 'Pasif'}
- Uygulama kategorisi: {app_category}
- Verimlilik skoru: %{productivity_score}

Lütfen bu öğrenciye kısa, motive edici ve kişiselleştirilmiş bir koçluk tavsiyesi ver. 
Hem görsel davranış hem de dijital aktiviteyi göz önünde bulundur."""
        
        content.append({
            "type": "text",
            "text": text_content
        })
        
        return content
    
    def generate_coaching_response(self, situation: str, context: Dict[str, Any]) -> str:
        """Ana koçluk yanıtı üret"""
        if self.model_loaded:
            return self._generate_real_response(situation, context)
        else:
            return self._generate_simulation_response(situation, context)
    
    def _generate_real_response(self, situation: str, context: Dict[str, Any]) -> str:
        """Gerçek Gemma 3N modeli ile yanıt üret"""
        try:
            # Get multimodal data
            image_data = context.get('image_data', None)
            audio_data = context.get('audio_data', None)
            
            # Create multimodal messages for Gemma 3N
            messages = [
                {
                    "role": "system",
                    "content": [{
                        "type": "text",
                        "text": """Sen gelişmiş bir AI eğitim koçusun. Öğrencilerin çalışma performansını artırmak, 
                        motivasyonlarını yüksek tutmak ve etkili öğrenme stratejileri geliştirmelerine yardım etmek için tasarlandın.
                        Kısa, net ve motive edici tavsiyelerde bulun. Türkçe yanıt ver."""
                    }]
                },
                {
                    "role": "user",
                    "content": self._build_multimodal_content(situation, context, image_data, audio_data)
                }
            ]
            
            # Use Gemma 3N's multimodal inference with recommended settings
            inputs = self.tokenizer.apply_chat_template(
                messages,
                add_generation_prompt=True,
                tokenize=True,
                return_dict=True,
                return_tensors="pt"
            ).to(self.device)
            
            with torch.no_grad():
                output_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=128,
                    temperature=1.0,  # Gemma 3N recommended settings
                    top_p=0.95,
                    top_k=64,
                    do_sample=True
                )
            
            # Decode response
            generated_text = self.tokenizer.decode(
                output_ids[0][inputs['input_ids'].shape[-1]:], 
                skip_special_tokens=True
            )
            
            # Cleanup
            del inputs, output_ids
            torch.cuda.empty_cache()
            gc.collect()
            
            return generated_text.strip() if generated_text.strip() else self._generate_simulation_response(situation, context)
            
        except Exception as e:
            print(f"⚠️ Gemma 3N generation error: {e}")
            return self._generate_simulation_response(situation, context)
    
    def _generate_simulation_response(self, situation: str, context: Dict[str, Any]) -> str:
        """Simülasyon yanıtı (model yüklü değilse)"""
        duration = context.get('duration', 0)
        attention_score = context.get('attention_score', 75)
        
        responses = {
            'start': [
                f"🚀 Harika! Yeni bir çalışma seansına başlıyoruz. Odaklanmaya hazır mısın?",
                f"💪 Motive olmuş görünüyorsun! Bu seansı verimli geçirelim.",
                f"🎯 Hedeflerine odaklan ve başarıya ulaş!"
            ],
            'progress': [
                f"👍 {duration} dakikadır çalışıyorsun, harika! Dikkat seviyesi: %{attention_score}",
                f"🔥 Süper gidiyorsun! {duration} dakika boyunca odaklandın.",
                f"⭐ Mükemmel performans! Böyle devam et."
            ],
            'distraction': [
                f"🎯 Dikkatini topla! %{attention_score} seviyesindesin, biraz daha odaklanabilirsin.",
                f"💡 Kısa bir mola ver ve tekrar odaklan. Sen yapabilirsin!",
                f"🧘‍♂️ Derin bir nefes al ve çalışmana geri dön."
            ],
            'end': [
                f"🎉 Tebrikler! {duration} dakikalık verimli bir seans tamamladın!",
                f"👏 Harika çalışma! {duration} dakika boyunca odaklandın.",
                f"🏆 Başarılı bir seans! Kendini ödüllendirebilirsin."
            ]
        }
        
        import random
        situation_responses = responses.get(situation, responses['progress'])
        return random.choice(situation_responses)
    
    def generate_audio_feedback(self, text: str) -> Optional[str]:
        """Sesli geri bildirim üret"""
        if not AUDIO_AVAILABLE:
            return None
        
        try:
            # Initialize TTS engine
            engine = pyttsx3.init()
            
            # Configure voice settings
            voices = engine.getProperty('voices')
            if voices:
                # Try to find a Turkish voice or use default
                for voice in voices:
                    if 'turkish' in voice.name.lower() or 'tr' in voice.id.lower():
                        engine.setProperty('voice', voice.id)
                        break
            
            # Set speech rate and volume
            engine.setProperty('rate', 150)  # Slower for better understanding
            engine.setProperty('volume', 0.8)
            
            # Generate audio file
            audio_file = f"feedback_{int(time.time())}.wav"
            engine.save_to_file(text, audio_file)
            engine.runAndWait()
            
            return audio_file
            
        except Exception as e:
            print(f"⚠️ Audio generation error: {e}")
            return None
    
    def process_audio_input(self, audio_data: np.ndarray, sample_rate: int = 16000) -> Dict[str, Any]:
        """Ses girişini işle"""
        if not AUDIO_AVAILABLE:
            return {'audio_processed': False}
        
        try:
            # Basic audio analysis
            audio_features = {
                'duration': len(audio_data) / sample_rate,
                'rms_energy': np.sqrt(np.mean(audio_data**2)),
                'zero_crossing_rate': np.mean(librosa.feature.zero_crossing_rate(audio_data)),
                'audio_processed': True
            }
            
            # Speech recognition (optional)
            try:
                r = sr.Recognizer()
                # Convert numpy array to audio data
                audio_bytes = io.BytesIO()
                sf.write(audio_bytes, audio_data, sample_rate, format='WAV')
                audio_bytes.seek(0)
                
                with sr.AudioFile(audio_bytes) as source:
                    audio = r.record(source)
                    text = r.recognize_google(audio, language='tr-TR')
                    audio_features['transcription'] = text
            except:
                audio_features['transcription'] = None
            
            return audio_features
            
        except Exception as e:
            print(f"⚠️ Audio processing error: {e}")
            return {'audio_processed': False}
    
    def get_model_info(self) -> Dict[str, Any]:
        """Model bilgilerini al"""
        return {
            'model_name': self.model_name,
            'model_loaded': self.model_loaded,
            'device': self.device,
            'audio_available': AUDIO_AVAILABLE,
            'mediapipe_available': MEDIAPIPE_AVAILABLE,
            'unsloth_available': UNSLOTH_AVAILABLE
        }


# Global instance for easy import
gemma_coach = None

def initialize_coach(model_name: str = "unsloth/Gemma-2-9B-It-GGUF") -> GemmaCoachCore:
    """Global coach instance'ı başlat"""
    global gemma_coach
    gemma_coach = GemmaCoachCore(model_name)
    return gemma_coach

def get_coach() -> Optional[GemmaCoachCore]:
    """Global coach instance'ı al"""
    return gemma_coach
