# 🚀 Gemma 3N AI Coach - Kaggle Modular Edition

Gelişmiş AI koçluk sistemi - Kaggle GPU ortamında çalışacak şekilde modüler yapıya dönüştürülmüş versiyonu.

## 📋 Özellikler

### 🤖 Gelişmiş AI Yetenekleri
- **Gemma 3N Multimodal**: Gerçek multimodal AI (text, vision, audio)
- **Akıllı Koçluk**: Kişiselleştirilmiş geri bildirim ve motivasyon
- **Türkçe Optimizasyonu**: Türkçe dil desteği ve kültürel uyum

### 🔊 Sesli Geri Bildirim
- **Text-to-Speech**: pyttsx3 ile Türkçe sesli mesajlar
- **Ses Tanıma**: Kullanıcı ses girişi analizi
- **Audio Analytics**: Ses tabanlı dikkat analizi

### 📺 İsteğe Bağlı Ekran İzleme
- **Gizlilik Odaklı**: <PERSON><PERSON><PERSON>ull<PERSON>ı<PERSON>ı istediğinde aktif
- **Uygulama <PERSON>**: Çalış<PERSON>, araş<PERSON><PERSON>rma, dikka<PERSON> dağı<PERSON>ıcı
- **Verimlilik Skoru**: Gerçek zamanlı produktivite analizi

### 📷 Kamera Dikkat Analizi
- **MediaPipe**: Gelişmiş yüz tespiti
- **Dikkat Skoru**: Yüz pozisyonu ve odaklanma analizi
- **Gerçek Zamanlı**: Sürekli dikkat takibi

### 📊 Gelişmiş Analytics
- **Session Tracking**: Detaylı çalışma seansı takibi
- **Günlük/Haftalık Raporlar**: İlerleme analizi
- **Hedef Takibi**: Kişisel hedefler ve başarı oranları

## 🏗️ Modüler Yapı

### Ana Modüller

1. **`gemma_coach_core.py`** - Ana AI sistemi
   - Gemma 3N model yönetimi
   - Multimodal mesaj işleme
   - Koçluk yanıtları üretimi
   - Audio feedback sistemi

2. **`screen_monitor.py`** - Ekran izleme
   - İsteğe bağlı ekran takibi
   - Uygulama kategorilendirme
   - Verimlilik analizi
   - Gizlilik kontrolleri

3. **`camera_monitor.py`** - Kamera sistemi
   - MediaPipe yüz tespiti
   - Dikkat skoru hesaplama
   - Gerçek zamanlı analiz
   - Baseline kalibrasyonu

4. **`session_manager.py`** - Seans yönetimi
   - Çalışma seansı koordinasyonu
   - İstatistik toplama
   - Hedef takibi
   - Veri export/import

5. **`kaggle_coach_notebook.ipynb`** - Ana Kaggle notebook
   - Tüm modülleri entegre eder
   - Gradio arayüzü
   - Kaggle-optimized kurulum
   - Kullanım kılavuzu

## 🚀 Kaggle'da Kullanım

### 1. Notebook'u Kaggle'a Yükle
```bash
# Tüm dosyaları Kaggle notebook ortamına yükle:
# - kaggle_coach_notebook.ipynb (ana notebook)
# - gemma_coach_core.py
# - screen_monitor.py  
# - camera_monitor.py
# - session_manager.py
```

### 2. GPU Ortamını Aktifleştir
- Kaggle notebook settings'den GPU'yu etkinleştir
- T4 x2 veya P100 önerilir

### 3. Notebook'u Çalıştır
```python
# Hücreleri sırayla çalıştır:
# 1. Kütüphane kurulumu
# 2. Modül import'ları  
# 3. Sistem başlatma
# 4. Model yükleme
# 5. Gradio arayüzü
```

### 4. Arayüzü Kullan
- Public link ile arayüze eriş
- Session başlat/bitir
- Ekran izlemeyi isteğe bağlı etkinleştir
- Sesli geri bildirimi aktifleştir

## 🔧 Kurulum Detayları

### Gerekli Kütüphaneler
```bash
# Ana kütüphaneler (otomatik yüklenir)
pip install unsloth[colab-new]
pip install gradio
pip install torch torchvision

# Computer Vision
pip install opencv-python mediapipe

# Audio (opsiyonel)
pip install pyttsx3 speechrecognition sounddevice soundfile librosa

# Screen Monitoring (opsiyonel)  
pip install pyautogui pygetwindow psutil

# Analytics
pip install plotly pandas numpy
```

### Kaggle Optimizasyonları
- **4-bit Quantization**: Memory efficient model loading
- **Batch Processing**: Optimized inference
- **Background Threading**: Non-blocking monitoring
- **Error Handling**: Graceful fallbacks

## 📱 Arayüz Özellikleri

### Sol Panel - Kontroller
- **Session Controls**: Start/End session buttons
- **Screen Monitoring**: Enable/disable controls
- **Audio Settings**: Voice feedback toggle
- **Status Displays**: Real-time status indicators

### Sağ Panel - AI Coach
- **AI Feedback**: Personalized coaching messages
- **Voice Output**: Audio feedback player
- **Progress Charts**: Visual analytics
- **System Info**: Technical details

## 🎯 Kullanım Senaryoları

### 1. Temel Çalışma Seansı
```python
# 1. Session başlat
# 2. AI coach'tan motivasyon al
# 3. Çalışmaya odaklan
# 4. Session bitir ve özet al
```

### 2. Gelişmiş İzleme
```python
# 1. Ekran izlemeyi etkinleştir
# 2. Kamera dikkat analizini başlat  
# 3. Sesli geri bildirimi aç
# 4. Tam spektrum AI coaching deneyimi
```

### 3. Analytics ve Raporlama
```python
# 1. Günlük ilerleme takibi
# 2. Haftalık performans analizi
# 3. Hedef belirleme ve takip
# 4. Veri export/import
```

## 🔒 Gizlilik ve Güvenlik

### Veri İşleme
- **Lokal İşleme**: Tüm veriler yerel olarak işlenir
- **Dış Gönderim Yok**: Hassas veriler dışarı gönderilmez
- **İsteğe Bağlı**: Ekran izleme tamamen opsiyonel
- **Kullanıcı Kontrolü**: Her özellik ayrı ayrı kontrol edilebilir

### Kamera ve Ekran
- **Açık İzin**: Sadece kullanıcı izni ile aktif
- **Görsel Gizlilik**: Sadece analiz verileri saklanır
- **Anında Durdurma**: İstediğiniz zaman kapatabilirsiniz

## 🐛 Sorun Giderme

### Model Yükleme Sorunları
```python
# GPU memory yetersizse:
# - 4-bit quantization kullan
# - Batch size'ı küçült
# - Model cache'i temizle
```

### Audio Sorunları
```python
# Ses çıkmıyorsa:
# - Audio kütüphanelerini kontrol et
# - Sistem ses ayarlarını kontrol et
# - Fallback text-only mode kullan
```

### Ekran İzleme Sorunları
```python
# İzleme çalışmıyorsa:
# - Platform uyumluluğunu kontrol et
# - İzin ayarlarını kontrol et
# - Simulation mode'a geç
```

## 📈 Performans Optimizasyonu

### Kaggle GPU Kullanımı
- **Memory Management**: Efficient VRAM usage
- **Batch Processing**: Optimized inference
- **Background Tasks**: Non-blocking operations
- **Resource Cleanup**: Automatic memory cleanup

### Model Optimizasyonu
- **Quantization**: 4-bit for memory efficiency
- **Caching**: Smart model caching
- **Inference Settings**: Optimized parameters
- **Fallback Modes**: Graceful degradation

## 🤝 Katkıda Bulunma

Bu modüler yapı sayesinde:
- Her modül bağımsız geliştirilebilir
- Yeni özellikler kolayca eklenebilir
- Test ve debug işlemleri basitleşir
- Kaggle ortamında optimize performans

## 📞 Destek

Sorunlar için:
1. Notebook'taki test hücrelerini çalıştır
2. System Information bölümünü kontrol et
3. Error mesajlarını incele
4. Fallback mode'ları dene

---

**🎉 Kaggle'da gelişmiş AI coaching deneyimine hazır mısın?**
