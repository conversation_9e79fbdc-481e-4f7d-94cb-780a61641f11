#!/usr/bin/env python3
"""
Session Manager <PERSON><PERSON><PERSON> seansı yönetimi ve koordina<PERSON>onu
"""

import time
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from collections import defaultdict

class SessionManager:
    """Çalışma seansı yöneticisi"""
    
    def __init__(self):
        self.active = False
        self.start_time = None
        self.session_count = 0
        self.total_study_time = 0
        
        # Session history
        self.session_history = []
        self.daily_stats = defaultdict(dict)
        
        # Goals and targets
        self.daily_goal_minutes = 120  # Default 2 hours
        self.session_goal_minutes = 25  # Pomodoro style
        
        print("📊 Session Manager initialized")
    
    def start_session(self) -> Dict[str, Any]:
        """Yeni çalı<PERSON> seans<PERSON> başlat"""
        if self.active:
            return {
                'success': False,
                'message': 'Zaten aktif bir seans var',
                'session_id': None
            }
        
        self.active = True
        self.start_time = time.time()
        self.session_count += 1
        
        session_id = f"session_{int(self.start_time)}"
        
        # Create session record
        session_record = {
            'session_id': session_id,
            'start_time': self.start_time,
            'start_datetime': datetime.now().isoformat(),
            'session_number': self.session_count,
            'goal_minutes': self.session_goal_minutes
        }
        
        print(f"🟢 Session {self.session_count} started")
        
        return {
            'success': True,
            'message': f'Seans {self.session_count} başlatıldı',
            'session_id': session_id,
            'session_record': session_record
        }
    
    def end_session(self) -> Dict[str, Any]:
        """Mevcut seansı sonlandır"""
        if not self.active:
            return {
                'success': False,
                'message': 'Aktif seans yok',
                'duration_minutes': 0,
                'session_summary': None
            }
        
        end_time = time.time()
        duration_seconds = end_time - self.start_time
        duration_minutes = duration_seconds / 60
        
        # Update totals
        self.total_study_time += duration_minutes
        
        # Create session summary
        session_summary = {
            'session_number': self.session_count,
            'start_time': self.start_time,
            'end_time': end_time,
            'duration_minutes': duration_minutes,
            'duration_seconds': duration_seconds,
            'goal_achieved': duration_minutes >= self.session_goal_minutes,
            'end_datetime': datetime.now().isoformat()
        }
        
        # Add to history
        self.session_history.append(session_summary)
        
        # Update daily stats
        today = datetime.now().strftime('%Y-%m-%d')
        if today not in self.daily_stats:
            self.daily_stats[today] = {
                'total_minutes': 0,
                'session_count': 0,
                'sessions': []
            }
        
        self.daily_stats[today]['total_minutes'] += duration_minutes
        self.daily_stats[today]['session_count'] += 1
        self.daily_stats[today]['sessions'].append(session_summary)
        
        # Reset session state
        self.active = False
        self.start_time = None
        
        print(f"🔴 Session {self.session_count} ended - {duration_minutes:.1f} minutes")
        
        return {
            'success': True,
            'message': f'Seans tamamlandı: {duration_minutes:.1f} dakika',
            'duration_minutes': duration_minutes,
            'session_summary': session_summary
        }
    
    def get_current_session_info(self) -> Dict[str, Any]:
        """Mevcut seans bilgilerini al"""
        if not self.active:
            return {
                'active': False,
                'duration_minutes': 0,
                'duration_seconds': 0,
                'session_number': 0,
                'progress_percentage': 0
            }
        
        current_time = time.time()
        duration_seconds = current_time - self.start_time
        duration_minutes = duration_seconds / 60
        
        # Calculate progress towards goal
        progress_percentage = min(100, (duration_minutes / self.session_goal_minutes) * 100)
        
        return {
            'active': True,
            'duration_minutes': duration_minutes,
            'duration_seconds': duration_seconds,
            'session_number': self.session_count,
            'progress_percentage': progress_percentage,
            'goal_minutes': self.session_goal_minutes,
            'remaining_minutes': max(0, self.session_goal_minutes - duration_minutes)
        }
    
    def get_daily_progress(self) -> Dict[str, Any]:
        """Günlük ilerlemeyi al"""
        today = datetime.now().strftime('%Y-%m-%d')
        today_stats = self.daily_stats.get(today, {
            'total_minutes': 0,
            'session_count': 0,
            'sessions': []
        })
        
        # Add current session if active
        current_minutes = 0
        if self.active:
            current_minutes = (time.time() - self.start_time) / 60
        
        total_today = today_stats['total_minutes'] + current_minutes
        progress_percentage = min(100, (total_today / self.daily_goal_minutes) * 100)
        
        return {
            'date': today,
            'total_minutes': total_today,
            'completed_sessions': today_stats['session_count'],
            'active_session_minutes': current_minutes,
            'daily_goal_minutes': self.daily_goal_minutes,
            'progress_percentage': progress_percentage,
            'remaining_minutes': max(0, self.daily_goal_minutes - total_today),
            'goal_achieved': total_today >= self.daily_goal_minutes
        }
    
    def get_weekly_summary(self) -> Dict[str, Any]:
        """Haftalık özet al"""
        today = datetime.now()
        week_start = today - timedelta(days=today.weekday())
        
        weekly_data = []
        total_week_minutes = 0
        total_week_sessions = 0
        
        for i in range(7):
            day = week_start + timedelta(days=i)
            day_str = day.strftime('%Y-%m-%d')
            day_stats = self.daily_stats.get(day_str, {
                'total_minutes': 0,
                'session_count': 0
            })
            
            weekly_data.append({
                'date': day_str,
                'day_name': day.strftime('%A'),
                'minutes': day_stats['total_minutes'],
                'sessions': day_stats['session_count']
            })
            
            total_week_minutes += day_stats['total_minutes']
            total_week_sessions += day_stats['session_count']
        
        return {
            'week_start': week_start.strftime('%Y-%m-%d'),
            'weekly_data': weekly_data,
            'total_minutes': total_week_minutes,
            'total_sessions': total_week_sessions,
            'average_daily_minutes': total_week_minutes / 7,
            'weekly_goal_minutes': self.daily_goal_minutes * 7,
            'weekly_progress': min(100, (total_week_minutes / (self.daily_goal_minutes * 7)) * 100)
        }
    
    def get_status_update(self) -> Dict[str, str]:
        """Durum güncellemesi al"""
        if not self.active:
            return {
                'status': '⚪ Ready to Start',
                'feedback': 'Yeni bir çalışma seansı başlatmaya hazır!',
                'duration': '0 dakika',
                'attention': 'Hazır'
            }
        
        session_info = self.get_current_session_info()
        duration_minutes = int(session_info['duration_minutes'])
        progress = session_info['progress_percentage']
        
        # Generate status based on progress
        if progress < 25:
            status = '🟢 Session Active'
            feedback = f'Harika başlangıç! {duration_minutes} dakikadır odaklanıyorsun.'
        elif progress < 50:
            status = '🟡 Making Progress'
            feedback = f'Süper gidiyorsun! {duration_minutes} dakika tamamlandı.'
        elif progress < 75:
            status = '🟠 Good Momentum'
            feedback = f'Mükemmel odaklanma! {duration_minutes} dakika boyunca çalışıyorsun.'
        else:
            status = '🔥 Excellent Focus'
            feedback = f'Harika performans! {duration_minutes} dakika sürekli odak.'
        
        return {
            'status': status,
            'feedback': feedback,
            'duration': f'{duration_minutes} dakika',
            'attention': f'%{int(progress)} ilerleme'
        }
    
    def set_goals(self, daily_minutes: int = None, session_minutes: int = None):
        """Hedefleri ayarla"""
        if daily_minutes:
            self.daily_goal_minutes = daily_minutes
            print(f"📊 Daily goal set to {daily_minutes} minutes")
        
        if session_minutes:
            self.session_goal_minutes = session_minutes
            print(f"📊 Session goal set to {session_minutes} minutes")
    
    def export_data(self) -> Dict[str, Any]:
        """Tüm verileri dışa aktar"""
        return {
            'session_history': self.session_history,
            'daily_stats': dict(self.daily_stats),
            'total_study_time': self.total_study_time,
            'session_count': self.session_count,
            'daily_goal_minutes': self.daily_goal_minutes,
            'session_goal_minutes': self.session_goal_minutes,
            'export_timestamp': datetime.now().isoformat()
        }
    
    def import_data(self, data: Dict[str, Any]):
        """Verileri içe aktar"""
        try:
            self.session_history = data.get('session_history', [])
            self.daily_stats = defaultdict(dict, data.get('daily_stats', {}))
            self.total_study_time = data.get('total_study_time', 0)
            self.session_count = data.get('session_count', 0)
            self.daily_goal_minutes = data.get('daily_goal_minutes', 120)
            self.session_goal_minutes = data.get('session_goal_minutes', 25)
            print("📊 Data imported successfully")
            return True
        except Exception as e:
            print(f"❌ Data import failed: {e}")
            return False
    
    def reset_all_data(self):
        """Tüm verileri sıfırla"""
        self.active = False
        self.start_time = None
        self.session_count = 0
        self.total_study_time = 0
        self.session_history = []
        self.daily_stats = defaultdict(dict)
        print("📊 All session data reset")


# Global instance for easy import
session_manager = None

def initialize_session_manager() -> SessionManager:
    """Global session manager instance'ı başlat"""
    global session_manager
    session_manager = SessionManager()
    return session_manager

def get_session_manager() -> Optional[SessionManager]:
    """Global session manager instance'ı al"""
    return session_manager

# Auto-initialize
if session_manager is None:
    session_manager = initialize_session_manager()
