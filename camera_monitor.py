#!/usr/bin/env python3
"""
Camera Monitor Module
Kamera tabanlı dikkat ve yüz tespiti
"""

import cv2
import time
import threading
import numpy as np
from typing import Dict, Any, Optional, Tuple
from collections import deque

# Computer vision imports
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
    print("✅ MediaPipe available")
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    print("⚠️ MediaPipe disabled - install: pip install mediapipe")


class CameraMonitor:
    """Kamera tabanlı dikkat izleme sistemi"""
    
    def __init__(self, camera_index: int = 0):
        self.camera_index = camera_index
        self.cap = None
        self.monitoring_active = False
        self.monitor_thread = None
        self.stop_event = threading.Event()
        
        # MediaPipe setup
        if MEDIAPIPE_AVAILABLE:
            self.mp_face_detection = mp.solutions.face_detection
            self.mp_drawing = mp.solutions.drawing_utils
            self.face_detection = self.mp_face_detection.FaceDetection(
                model_selection=0, min_detection_confidence=0.5
            )
        
        # Tracking variables
        self.attention_history = deque(maxlen=50)  # Last 50 measurements
        self.face_detected_history = deque(maxlen=20)  # Last 20 face detections
        self.current_attention_score = 75
        self.current_face_detected = False
        
        # Calibration
        self.baseline_established = False
        self.baseline_measurements = []
        
        print("📷 Camera Monitor initialized")
    
    def start_monitoring(self) -> bool:
        """Kamera izlemeyi başlat"""
        if self.monitoring_active:
            return True
        
        try:
            # Initialize camera
            self.cap = cv2.VideoCapture(self.camera_index)
            if not self.cap.isOpened():
                print("❌ Camera could not be opened")
                return False
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, 15)
            
            self.monitoring_active = True
            self.stop_event.clear()
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            print("📷 Camera monitoring started")
            return True
            
        except Exception as e:
            print(f"❌ Camera initialization failed: {e}")
            return False
    
    def stop_monitoring(self):
        """Kamera izlemeyi durdur"""
        self.monitoring_active = False
        self.stop_event.set()
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        
        if self.cap:
            self.cap.release()
            self.cap = None
        
        print("📷 Camera monitoring stopped")
    
    def _monitor_loop(self):
        """Ana izleme döngüsü"""
        frame_count = 0
        
        while self.monitoring_active and not self.stop_event.is_set():
            try:
                ret, frame = self.cap.read()
                if not ret:
                    print("⚠️ Failed to read camera frame")
                    time.sleep(0.1)
                    continue
                
                frame_count += 1
                
                # Process every 3rd frame to reduce CPU usage
                if frame_count % 3 == 0:
                    self._process_frame(frame)
                
                time.sleep(0.1)  # ~10 FPS processing
                
            except Exception as e:
                print(f"⚠️ Camera monitoring error: {e}")
                time.sleep(1)
    
    def _process_frame(self, frame: np.ndarray):
        """Kare işleme ve analiz"""
        if not MEDIAPIPE_AVAILABLE:
            # Fallback: simple motion detection
            self._simple_attention_detection(frame)
            return
        
        # Convert BGR to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Face detection
        results = self.face_detection.process(rgb_frame)
        
        face_detected = False
        attention_score = 0
        
        if results.detections:
            face_detected = True
            
            # Calculate attention score based on face position and size
            for detection in results.detections:
                bbox = detection.location_data.relative_bounding_box
                
                # Face position (center of frame is ideal)
                face_center_x = bbox.xmin + bbox.width / 2
                face_center_y = bbox.ymin + bbox.height / 2
                
                # Distance from center (0.5, 0.5)
                center_distance = np.sqrt((face_center_x - 0.5)**2 + (face_center_y - 0.5)**2)
                
                # Face size (larger is better, indicates closer to camera)
                face_size = bbox.width * bbox.height
                
                # Calculate attention score
                position_score = max(0, 100 - (center_distance * 200))  # Penalty for being off-center
                size_score = min(100, face_size * 1000)  # Reward for being close
                
                attention_score = (position_score * 0.6 + size_score * 0.4)
                break  # Use first detected face
        
        # Update tracking
        self.current_face_detected = face_detected
        self.face_detected_history.append(face_detected)
        
        if face_detected:
            self.attention_history.append(attention_score)
            self.current_attention_score = int(np.mean(list(self.attention_history)[-10:]))  # Average of last 10
        else:
            # Gradually decrease attention score when no face detected
            self.current_attention_score = max(0, self.current_attention_score - 5)
    
    def _simple_attention_detection(self, frame: np.ndarray):
        """Basit dikkat tespiti (MediaPipe olmadan)"""
        # Simple motion-based attention detection
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Use frame difference for motion detection
        if hasattr(self, '_prev_frame'):
            diff = cv2.absdiff(self._prev_frame, gray)
            motion_score = np.mean(diff)
            
            # Convert motion to attention score (less motion = more attention)
            attention_score = max(0, 100 - (motion_score * 2))
            self.current_attention_score = int(attention_score)
            self.current_face_detected = motion_score < 30  # Assume face present if low motion
        
        self._prev_frame = gray.copy()
    
    def get_current_status(self) -> Dict[str, Any]:
        """Mevcut kamera durumunu al"""
        if not self.monitoring_active:
            return {
                'monitoring_active': False,
                'face_detected': False,
                'attention_score': 0,
                'camera_status': 'Inactive'
            }
        
        # Calculate face detection stability
        recent_detections = list(self.face_detected_history)[-10:]
        face_stability = sum(recent_detections) / len(recent_detections) if recent_detections else 0
        
        return {
            'monitoring_active': True,
            'face_detected': self.current_face_detected,
            'attention_score': self.current_attention_score,
            'face_stability': face_stability,
            'camera_status': 'Active',
            'mediapipe_available': MEDIAPIPE_AVAILABLE
        }
    
    def get_attention_summary(self) -> Dict[str, Any]:
        """Dikkat özeti al"""
        if not self.attention_history:
            return {
                'average_attention': 0,
                'max_attention': 0,
                'min_attention': 0,
                'attention_trend': 'stable',
                'total_measurements': 0
            }
        
        attention_scores = list(self.attention_history)
        
        # Calculate statistics
        avg_attention = np.mean(attention_scores)
        max_attention = np.max(attention_scores)
        min_attention = np.min(attention_scores)
        
        # Calculate trend (last 10 vs previous 10)
        if len(attention_scores) >= 20:
            recent_avg = np.mean(attention_scores[-10:])
            previous_avg = np.mean(attention_scores[-20:-10])
            
            if recent_avg > previous_avg + 5:
                trend = 'improving'
            elif recent_avg < previous_avg - 5:
                trend = 'declining'
            else:
                trend = 'stable'
        else:
            trend = 'insufficient_data'
        
        return {
            'average_attention': int(avg_attention),
            'max_attention': int(max_attention),
            'min_attention': int(min_attention),
            'attention_trend': trend,
            'total_measurements': len(attention_scores)
        }
    
    def capture_frame(self) -> Optional[np.ndarray]:
        """Mevcut kareyi yakala"""
        if not self.monitoring_active or not self.cap:
            return None
        
        try:
            ret, frame = self.cap.read()
            if ret:
                return frame
        except Exception as e:
            print(f"⚠️ Frame capture error: {e}")
        
        return None
    
    def calibrate_baseline(self, duration_seconds: int = 10):
        """Dikkat baseline'ı kalibre et"""
        print(f"📷 Starting {duration_seconds}s calibration...")
        
        self.baseline_measurements = []
        start_time = time.time()
        
        while time.time() - start_time < duration_seconds:
            if self.current_face_detected:
                self.baseline_measurements.append(self.current_attention_score)
            time.sleep(0.5)
        
        if self.baseline_measurements:
            baseline_avg = np.mean(self.baseline_measurements)
            print(f"📷 Baseline established: {baseline_avg:.1f}")
            self.baseline_established = True
        else:
            print("⚠️ Calibration failed - no face detected")
    
    def reset_history(self):
        """Geçmişi temizle"""
        self.attention_history.clear()
        self.face_detected_history.clear()
        self.baseline_measurements = []
        self.baseline_established = False
        print("📷 Camera monitoring history cleared")


# Global instance for easy import
camera_monitor = None

def initialize_camera_monitor(camera_index: int = 0) -> CameraMonitor:
    """Global camera monitor instance'ı başlat"""
    global camera_monitor
    camera_monitor = CameraMonitor(camera_index)
    return camera_monitor

def get_camera_monitor() -> Optional[CameraMonitor]:
    """Global camera monitor instance'ı al"""
    return camera_monitor

# Auto-initialize
if camera_monitor is None:
    camera_monitor = initialize_camera_monitor()
