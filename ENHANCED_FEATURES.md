# 🚀 Enhanced Gemma 3N AI Coaching System

## 📋 Overview

Bu gelişmiş AI koçluk sistemi, Gemma 3N'in gerçek multimodal yeteneklerini kullanarak daha etkili ve kişiselleştirilmiş bir öğrenme deneyimi sunar.

## ✨ Yeni Özellikler

### 🔊 Se<PERSON>li G<PERSON>il<PERSON>im (Audio Feedback)
- **Text-to-Speech (TTS)**: AI koçunuzdan sesli mesajlar alın
- **Türkçe Destek**: Türkçe sesli geri bildirim
- **Ayarlanabilir Ses**: Hız ve ses seviyesi kontrolü
- **İsteğe Bağlı**: Sesli geri bildirimi açıp kapatabilirsiniz

### 📺 İsteğe Bağlı Ekran İzleme (Optional Screen Monitoring)
- **Kullanıcı Kontrolü**: Ekran izlemeyi istediğiniz zaman açıp kapatın
- **Gizlilik Odaklı**: Sadece siz etkinleştirdiğinizde çalışır
- **Verimlilik Analizi**: <PERSON><PERSON> uygulamaları kullandığınızı analiz eder
- **Kategori Bazlı Puanlama**: Çalışma, araştırma, dikkat dağıtıcı aktiviteler

### 🤖 Gelişmiş Gemma 3N Entegrasyonu
- **Gerçek Multimodal**: Metin, görüntü ve ses verilerini birlikte işler
- **Doğru Chat Template**: Gemma 3N'in önerilen mesaj formatını kullanır
- **Optimize Edilmiş Ayarlar**: temperature=1.0, top_p=0.95, top_k=64
- **Türkçe Optimizasyonu**: Türkçe yanıtlar için özel sistem mesajları

## 🛠️ Teknik Geliştirmeler

### Multimodal İçerik Oluşturma
```python
def _build_multimodal_content(self, situation, context, image_data, audio_data):
    content = []
    
    # Görüntü verisi ekle
    if image_data:
        content.append({"type": "image", "image": image_data})
    
    # Ses verisi ekle  
    if audio_data:
        content.append({"type": "audio", "audio": audio_data})
    
    # Metin içeriği ekle
    content.append({"type": "text", "text": context_text})
    
    return content
```

### Sesli Geri Bildirim Sistemi
```python
def generate_audio_feedback(self, text: str) -> Optional[str]:
    engine = pyttsx3.init()
    
    # Türkçe ses ayarları
    voices = engine.getProperty('voices')
    for voice in voices:
        if 'turkish' in voice.name.lower():
            engine.setProperty('voice', voice.id)
            break
    
    # Ses dosyası oluştur
    audio_file = f"feedback_{int(time.time())}.wav"
    engine.save_to_file(text, audio_file)
    engine.runAndWait()
    
    return audio_file
```

### İsteğe Bağlı Ekran İzleme
```python
class OptionalScreenMonitor:
    def enable_monitoring(self, enabled: bool = True):
        self.monitoring_enabled = enabled
        if not enabled and self.monitoring_active:
            self.stop_monitoring()
        return f"Ekran izleme {'etkinleştirildi' if enabled else 'devre dışı bırakıldı'}"
```

## 🎯 Kullanım Kılavuzu

### 1. Sistem Başlatma
```bash
# Gerekli kütüphaneleri yükle
pip install pyttsx3 speechrecognition sounddevice soundfile librosa
pip install pyautogui pygetwindow

# Jupyter notebook'u çalıştır
jupyter notebook working_gradio_coach.ipynb
```

### 2. Sesli Geri Bildirimi Etkinleştirme
- Gradio arayüzünde "🔊 Sesli Geri Bildirim" kutusunu işaretleyin
- AI koçunuzdan gelen mesajlar artık sesli olarak da gelecek

### 3. Ekran İzlemeyi Kontrol Etme
- "📺 Ekran İzlemeyi Etkinleştir" butonuna tıklayın
- İstemediğiniz zaman "🚫 Ekran İzlemeyi Kapat" ile kapatın

### 4. Çalışma Seansı Başlatma
- "🟢 Start Study Session" butonuna tıklayın
- AI koç sizi izlemeye başlar ve kişiselleştirilmiş tavsiyelerde bulunur

## 📊 Performans Metrikleri

### Dikkat Analizi
- **Yüz Tespiti**: Kameradan yüzünüzü tespit eder
- **Dikkat Skoru**: %0-100 arası dikkat seviyesi
- **Gerçek Zamanlı**: Anlık geri bildirim

### Ekran Aktivite Analizi (İsteğe Bağlı)
- **Uygulama Kategorileri**: Çalışma, araştırma, dikkat dağıtıcı
- **Verimlilik Skoru**: Aktivitelerinize göre %0-100 puan
- **Trend Analizi**: Zaman içindeki değişimler

### Sesli Geri Bildirim
- **Anında Ses**: Mesajlar hem metin hem ses olarak gelir
- **Türkçe TTS**: Doğal Türkçe telaffuz
- **Dosya Kaydetme**: Ses dosyaları otomatik kaydedilir

## 🔧 Sorun Giderme

### Ses Çalışmıyor
```bash
# Ses kütüphanelerini kontrol et
pip install --upgrade pyttsx3 speechrecognition
```

### Ekran İzleme Çalışmıyor
```bash
# Windows için
pip install pywin32 pyautogui pygetwindow

# Güvenlik ayarlarını kontrol et
```

### Model Yüklenmiyor
```bash
# Unsloth'u güncelle
pip install --upgrade unsloth

# CUDA sürümünü kontrol et
python -c "import torch; print(torch.cuda.is_available())"
```

## 🎉 Sonuç

Bu gelişmiş sistem artık:
- ✅ Gemma 3N'in gerçek multimodal yeteneklerini kullanıyor
- ✅ Sesli geri bildirim veriyor
- ✅ İsteğe bağlı ekran izleme sunuyor
- ✅ Türkçe optimize edilmiş yanıtlar üretiyor
- ✅ Gizlilik odaklı tasarıma sahip

Artık daha etkili ve kişiselleştirilmiş bir AI koçluk deneyimi yaşayabilirsiniz! 🚀
