#!/usr/bin/env python3
"""
Screen Monitor Module
İ<PERSON>ğe bağlı ekran izleme ve aktivite analizi
"""

import time
import threading
from typing import Dict, Any, Optional
from collections import deque, defaultdict

# Screen monitoring imports (optional)
try:
    import pyautogui
    import pygetwindow as gw
    import psutil
    SCREEN_MONITORING_AVAILABLE = True
    print("✅ Screen monitoring libraries ready")
except ImportError:
    SCREEN_MONITORING_AVAILABLE = False
    print("⚠️ Screen monitoring disabled - install: pip install pyautogui pygetwindow psutil")

# Windows specific imports
try:
    import win32gui
    import win32process
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False


class OptionalScreenMonitor:
    """İsteğe bağlı ekran izleme sistemi"""
    
    def __init__(self):
        self.monitoring_enabled = False
        self.monitoring_active = False
        self.screen_history = deque(maxlen=100)
        self.monitor_thread = None
        self.stop_event = threading.Event()
        
        # Productivity categories
        self.productivity_apps = {
            'study': ['code', 'jupyter', 'notebook', 'pdf', 'reader', 'book', 'study', 'vscode', 'pycharm', 'sublime'],
            'productive': ['word', 'excel', 'powerpoint', 'docs', 'sheets', 'slides', 'office', 'notion', 'obsidian'],
            'research': ['browser', 'chrome', 'firefox', 'edge', 'research', 'wikipedia', 'scholar', 'arxiv'],
            'communication': ['teams', 'zoom', 'slack', 'discord', 'whatsapp', 'telegram', 'mail'],
            'distraction': ['game', 'social', 'facebook', 'youtube', 'instagram', 'tiktok', 'netflix', 'twitch']
        }
        
        # Activity tracking
        self.activity_stats = defaultdict(int)
        self.last_activity_time = time.time()
        
        print("📺 Optional Screen Monitor initialized")
    
    def enable_monitoring(self, enabled: bool = True) -> str:
        """Ekran izlemeyi etkinleştir/devre dışı bırak"""
        self.monitoring_enabled = enabled
        if not enabled and self.monitoring_active:
            self.stop_monitoring()
        return f"Ekran izleme {'etkinleştirildi' if enabled else 'devre dışı bırakıldı'}"
    
    def start_monitoring(self) -> bool:
        """Ekran izlemeyi başlat"""
        if not self.monitoring_enabled or not SCREEN_MONITORING_AVAILABLE:
            return False
        
        if not self.monitoring_active:
            self.monitoring_active = True
            self.stop_event.clear()
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            print("📺 Screen monitoring started")
        return True
    
    def stop_monitoring(self):
        """Ekran izlemeyi durdur"""
        self.monitoring_active = False
        self.stop_event.set()
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        print("📺 Screen monitoring stopped")
    
    def _monitor_loop(self):
        """Arka plan izleme döngüsü"""
        while self.monitoring_active and not self.stop_event.is_set():
            try:
                if SCREEN_MONITORING_AVAILABLE:
                    # Get active window
                    active_window = self._get_active_window()
                    if active_window:
                        app_name = active_window.lower()
                        category = self._categorize_app(app_name)
                        
                        # Record activity
                        activity_entry = {
                            'timestamp': time.time(),
                            'app_name': app_name,
                            'category': category,
                            'window_title': active_window
                        }
                        
                        self.screen_history.append(activity_entry)
                        self.activity_stats[category] += 1
                        self.last_activity_time = time.time()
                
                time.sleep(2)  # Check every 2 seconds
                
            except Exception as e:
                print(f"⚠️ Screen monitoring error: {e}")
                time.sleep(5)
    
    def _get_active_window(self) -> Optional[str]:
        """Aktif pencere başlığını al"""
        try:
            if WIN32_AVAILABLE:
                # Windows specific method
                hwnd = win32gui.GetForegroundWindow()
                window_title = win32gui.GetWindowText(hwnd)
                return window_title
            else:
                # Cross-platform method
                active_window = gw.getActiveWindow()
                if active_window:
                    return active_window.title
        except Exception as e:
            print(f"⚠️ Error getting active window: {e}")
        
        return None
    
    def _categorize_app(self, app_name: str) -> str:
        """Uygulamayı kategorize et"""
        app_name = app_name.lower()
        
        for category, keywords in self.productivity_apps.items():
            if any(keyword in app_name for keyword in keywords):
                return category
        
        return 'other'
    
    def get_current_screen_analysis(self) -> Dict[str, Any]:
        """Mevcut ekran analizini al"""
        if not self.monitoring_active or not self.screen_history:
            return {
                'app_category': 'bilinmiyor',
                'productivity_score': 0,
                'monitoring_active': False,
                'current_app': 'Bilinmiyor'
            }
        
        # Analyze recent activity (last 5 minutes)
        recent_time = time.time() - 300
        recent_activity = [entry for entry in self.screen_history if entry['timestamp'] > recent_time]
        
        if not recent_activity:
            return {
                'app_category': 'bilinmiyor',
                'productivity_score': 0,
                'monitoring_active': True,
                'current_app': 'Aktivite yok'
            }
        
        # Calculate productivity score
        category_counts = defaultdict(int)
        for entry in recent_activity:
            category = entry['category']
            category_counts[category] += 1
        
        total_entries = len(recent_activity)
        productivity_score = (
            (category_counts.get('study', 0) * 100 +
             category_counts.get('productive', 0) * 80 +
             category_counts.get('research', 0) * 60 +
             category_counts.get('communication', 0) * 40 +
             category_counts.get('other', 0) * 30 +
             category_counts.get('distraction', 0) * 0) / total_entries
        )
        
        # Get most common category and current app
        most_common_category = max(category_counts, key=category_counts.get) if category_counts else 'bilinmiyor'
        current_app = recent_activity[-1]['app_name'] if recent_activity else 'Bilinmiyor'
        
        return {
            'app_category': most_common_category,
            'productivity_score': int(productivity_score),
            'monitoring_active': True,
            'current_app': current_app,
            'category_distribution': dict(category_counts),
            'total_activities': total_entries
        }
    
    def get_productivity_summary(self) -> Dict[str, Any]:
        """Verimlilik özetini al"""
        if not self.screen_history:
            return {
                'total_time': 0,
                'productive_time': 0,
                'distraction_time': 0,
                'productivity_percentage': 0,
                'top_categories': {}
            }
        
        # Calculate time spent in each category
        category_time = defaultdict(float)
        total_time = 0
        
        for i, entry in enumerate(self.screen_history):
            if i > 0:
                time_diff = entry['timestamp'] - self.screen_history[i-1]['timestamp']
                if time_diff < 60:  # Only count if less than 1 minute gap
                    category_time[entry['category']] += time_diff
                    total_time += time_diff
        
        # Calculate productivity metrics
        productive_time = (
            category_time.get('study', 0) + 
            category_time.get('productive', 0) + 
            category_time.get('research', 0)
        )
        
        distraction_time = category_time.get('distraction', 0)
        
        productivity_percentage = (productive_time / total_time * 100) if total_time > 0 else 0
        
        return {
            'total_time': total_time / 60,  # Convert to minutes
            'productive_time': productive_time / 60,
            'distraction_time': distraction_time / 60,
            'productivity_percentage': productivity_percentage,
            'top_categories': dict(sorted(category_time.items(), key=lambda x: x[1], reverse=True))
        }
    
    def get_status(self) -> str:
        """Mevcut durumu al"""
        if not self.monitoring_enabled:
            return "📺 Devre dışı - isteğe bağlı etkinleştirilebilir"
        elif not self.monitoring_active:
            return "📺 Etkin ama izlemiyor - başlatılmadı"
        else:
            analysis = self.get_current_screen_analysis()
            app_category = analysis.get('app_category', 'bilinmiyor')
            productivity_score = analysis.get('productivity_score', 0)
            return f"📺 Aktif - {app_category.title()} (%{productivity_score} verimli)"
    
    def reset_history(self):
        """Geçmişi temizle"""
        self.screen_history.clear()
        self.activity_stats.clear()
        print("📺 Screen monitoring history cleared")


# Global instance for easy import
screen_monitor = None

def initialize_screen_monitor() -> OptionalScreenMonitor:
    """Global screen monitor instance'ı başlat"""
    global screen_monitor
    screen_monitor = OptionalScreenMonitor()
    return screen_monitor

def get_screen_monitor() -> Optional[OptionalScreenMonitor]:
    """Global screen monitor instance'ı al"""
    return screen_monitor

# Auto-initialize
if screen_monitor is None:
    screen_monitor = initialize_screen_monitor()
